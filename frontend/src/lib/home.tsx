import { FirstBentoAnimation } from '@/components/home/<USER>';
import { FourthBentoAnimation } from '@/components/home/<USER>';
import { SecondBentoAnimation } from '@/components/home/<USER>';
import { ThirdBentoAnimation } from '@/components/home/<USER>';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { Globe } from '@/components/home/<USER>/globe';
import { cn } from '@/lib/utils';
import { motion } from 'motion/react';
import { config } from '@/lib/config';

export const Highlight = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <span
      className={cn(
        'p-1 py-0.5 font-medium dark:font-semibold text-secondary',
        className,
      )}
    >
      {children}
    </span>
  );
};

export const BLUR_FADE_DELAY = 0.15;

interface UpgradePlan {
  /** @deprecated */
  hours: string;
  price: string;
  stripePriceId: string;
}

export interface PricingTier {
  name: string;
  price: string;
  yearlyPrice?: string; // Add yearly price support
  description: string;
  buttonText: string;
  buttonColor: string;
  isPopular: boolean;
  /** @deprecated */
  hours: string;
  features: string[];
  stripePriceId: string;
  yearlyStripePriceId?: string; // Add yearly price ID support
  upgradePlans: UpgradePlan[];
  hidden?: boolean; // Optional property to hide plans from display while keeping them in code
  billingPeriod?: 'monthly' | 'yearly'; // Add billing period support
  originalYearlyPrice?: string; // For showing crossed-out price
  discountPercentage?: number; // For showing discount badge
}

export const siteConfig = {
  name: ' Atlas',
  description: 'The Generalist AI Agent that can act on your behalf.',
  cta: 'Start Free',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  keywords: ['AI Agent', 'Generalist AI', 'Open Source AI', 'Autonomous Agent'],
  links: {
    email: '<EMAIL>',
    twitter: 'https://x.com/Atlasai',
    discord: 'https://discord.gg/Atlasai',
    github: 'https://github.com/-ai/Atlas',
    instagram: 'https://instagram.com/Atlasai',
  },
  nav: {
    links: [
      { id: 1, name: 'Home', href: '#hero' },
      { id: 2, name: 'Use Cases', href: '#use-cases' },
      { id: 3, name: 'Open Source', href: '#open-source' },
      { id: 4, name: 'Pricing', href: '#pricing' },
    ],
  },
  hero: {
    badgeIcon: (
      <svg
        width="14"
        height="14"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="text-muted-foreground"
      >
        <path
          d="M7.62758 1.09876C7.74088 1.03404 7.8691 1 7.99958 1C8.13006 1 8.25828 1.03404 8.37158 1.09876L13.6216 4.09876C13.7363 4.16438 13.8316 4.25915 13.8979 4.37347C13.9642 4.48779 13.9992 4.6176 13.9992 4.74976C13.9992 4.88191 13.9642 5.01172 13.8979 5.12604C13.8316 5.24036 13.7363 5.33513 13.6216 5.40076L8.37158 8.40076C8.25828 8.46548 8.13006 8.49952 7.99958 8.49952C7.8691 8.49952 7.74088 8.46548 7.62758 8.40076L2.37758 5.40076C2.26287 5.33513 2.16753 5.24036 2.10123 5.12604C2.03492 5.01172 2 4.88191 2 4.74976C2 4.6176 2.03492 4.48779 2.10123 4.37347C2.16753 4.25915 2.26287 4.16438 2.37758 4.09876L7.62758 1.09876Z"
          stroke="currentColor"
          strokeWidth="1.25"
        />
        <path
          d="M2.56958 7.23928L2.37758 7.34928C2.26287 7.41491 2.16753 7.50968 2.10123 7.624C2.03492 7.73831 2 7.86813 2 8.00028C2 8.13244 2.03492 8.26225 2.10123 8.37657C2.16753 8.49089 2.26287 8.58566 2.37758 8.65128L7.62758 11.6513C7.74088 11.716 7.8691 11.75 7.99958 11.75C8.13006 11.75 8.25828 11.716 8.37158 11.6513L13.6216 8.65128C13.7365 8.58573 13.8321 8.49093 13.8986 8.3765C13.965 8.26208 14 8.13211 14 7.99978C14 7.86745 13.965 7.73748 13.8986 7.62306C13.8321 7.50864 13.7365 7.41384 13.6216 7.34828L13.4296 7.23828L9.11558 9.70328C8.77568 9.89744 8.39102 9.99956 7.99958 9.99956C7.60814 9.99956 7.22347 9.89744 6.88358 9.70328L2.56958 7.23928Z"
          stroke="currentColor"
          strokeWidth="1.25"
        />
        <path
          d="M2.37845 10.5993L2.57045 10.4893L6.88445 12.9533C7.22435 13.1474 7.60901 13.2496 8.00045 13.2496C8.39189 13.2496 8.77656 13.1474 9.11645 12.9533L13.4305 10.4883L13.6225 10.5983C13.7374 10.6638 13.833 10.7586 13.8994 10.8731C13.9659 10.9875 14.0009 11.1175 14.0009 11.2498C14.0009 11.3821 13.9659 11.5121 13.8994 11.6265C13.833 11.7409 13.7374 11.8357 13.6225 11.9013L8.37245 14.9013C8.25915 14.966 8.13093 15 8.00045 15C7.86997 15 7.74175 14.966 7.62845 14.9013L2.37845 11.9013C2.2635 11.8357 2.16795 11.7409 2.10148 11.6265C2.03501 11.5121 2 11.3821 2 11.2498C2 11.1175 2.03501 10.9875 2.10148 10.8731C2.16795 10.7586 2.2635 10.6638 2.37845 10.5983V10.5993Z"
          stroke="currentColor"
          strokeWidth="1.25"
        />
      </svg>
    ),
    badge: '100% OPEN SOURCE',
    githubUrl: 'https://github.com/Atlas-ai/Atlas',
    title: 'Atlas, the AI Employee.',
    description:
      'Atlas by  – is a generalist AI Agent that acts on your behalf.',
    inputPlaceholder: 'Ask Atlas to...',
  },
  cloudPricingItems: [
    {
      name: 'Free',
      price: '$0',
      description: 'Perfect for getting started',
      buttonText: 'Start Free',
      buttonColor: 'bg-secondary text-white',
      isPopular: false,
      /** @deprecated */
      hours: '60 min',
      features: [
        '$5 free AI tokens included',
        'Public projects',
        'Basic Models',
        'Community support',
      ],
      stripePriceId: config.SUBSCRIPTION_TIERS.FREE.priceId,
      upgradePlans: [],
    },
    {
      name: 'Plus',
      price: '$20',
      description: 'Best for individuals and small teams',
      buttonText: 'Start Free',
      buttonColor: 'bg-primary text-white dark:text-black',
      isPopular: true,
      /** @deprecated */
      hours: '2 hours',
      features: [
        '$20 AI token credits/month',
        'Private projects',
        'Premium AI Models',
        'Community support',
      ],
      stripePriceId: config.SUBSCRIPTION_TIERS.PLUS.priceId,
      upgradePlans: [],
    },
    {
      name: 'Pro',
      price: '$75',
      description: 'Ideal for growing businesses',
      buttonText: 'Start Free',
      buttonColor: 'bg-secondary text-white',
      isPopular: false,
      /** @deprecated */
      hours: '6 hours',
      features: [
        '$75 AI token credits/month',
        'Private projects',
        'Premium AI Models',
        'Priority support',
      ],
      stripePriceId: config.SUBSCRIPTION_TIERS.PRO.priceId,
      upgradePlans: [],
    },
  ],
  companyShowcase: {
    companyLogos: [
      {
        id: 1,
        name: 'Company 1',
        logo: (
          <svg
            width="110"
            height="31"
            viewBox="0 0 110 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <path d="M34.5469 14.5155C34.5469 19.4338 37.7054 22.8631 42.0822 22.8631C46.4591 22.8631 49.6176 19.4338 49.6176 14.5155C49.6176 9.59721 46.4591 6.16797 42.0822 6.16797C37.7054 6.16797 34.5469 9.59721 34.5469 14.5155ZM46.7298 14.5155C46.7298 18.035 44.8121 20.3137 42.0822 20.3137C39.3524 20.3137 37.4347 18.035 37.4347 14.5155C37.4347 10.996 39.3524 8.71736 42.0822 8.71736C44.8121 8.71736 46.7298 10.996 46.7298 14.5155Z" />
            <path d="M57.7468 22.8652C61.0633 22.8652 62.9584 20.0676 62.9584 16.706C62.9584 13.3444 61.0633 10.5469 57.7468 10.5469C56.2127 10.5469 55.0846 11.156 54.3401 12.0359V10.7725H51.6328V26.678H54.3401V21.3761C55.0846 22.256 56.2127 22.8652 57.7468 22.8652ZM54.2724 16.3676C54.2724 14.1341 55.5359 12.9158 57.2054 12.9158C59.1682 12.9158 60.2285 14.4499 60.2285 16.706C60.2285 18.9621 59.1682 20.4963 57.2054 20.4963C55.5359 20.4963 54.2724 19.2554 54.2724 17.067V16.3676Z" />
            <path d="M70.2843 22.8652C72.6532 22.8652 74.5258 21.6243 75.3605 19.5487L73.0367 18.6688C72.6758 19.8871 71.6154 20.5639 70.2843 20.5639C68.5471 20.5639 67.3288 19.3231 67.1258 17.2926H75.4282V16.3902C75.4282 13.1414 73.6008 10.5469 70.1715 10.5469C66.7422 10.5469 64.5312 13.2316 64.5312 16.706C64.5312 20.3609 66.9002 22.8652 70.2843 22.8652ZM70.1489 12.8255C71.8636 12.8255 72.6758 13.9536 72.6983 15.2621H67.2611C67.6672 13.6603 68.7501 12.8255 70.1489 12.8255Z" />
            <path d="M77.4609 22.617H80.1683V15.6682C80.1683 13.9761 81.4091 13.0737 82.6274 13.0737C84.1164 13.0737 84.703 14.1341 84.703 15.6005V22.617H87.4103V14.8109C87.4103 12.2615 85.9213 10.5469 83.4396 10.5469C81.9054 10.5469 80.8451 11.2463 80.1683 12.0359V10.7725H77.4609V22.617Z" />
            <path d="M95.3397 6.41797L89.2031 22.6167H92.0684L93.4446 18.9167H100.438L101.837 22.6167H104.748L98.611 6.41797H95.3397ZM96.919 9.62163L99.4909 16.3899H94.3921L96.919 9.62163Z" />
            <path d="M109.396 6.46484H106.508V22.6636H109.396V6.46484Z" />
            <path d="M27.9278 12.3665C28.6102 10.3182 28.3752 8.07433 27.2838 6.21115C25.6425 3.35343 22.343 1.88321 19.1205 2.57508C17.687 0.960086 15.6273 0.0416664 13.4681 0.054827C10.1742 0.0473067 7.25158 2.16804 6.2382 5.30213C4.12219 5.73551 2.29568 7.06002 1.22685 8.93727C-0.426682 11.7875 -0.0497272 15.3803 2.15937 17.8244C1.4769 19.8728 1.71191 22.1166 2.8033 23.9798C4.4446 26.8375 7.74416 28.3078 10.9666 27.6159C12.3992 29.2309 14.4598 30.1494 16.6191 30.1352C19.9149 30.1437 22.8384 28.021 23.8518 24.8841C25.9678 24.4508 27.7943 23.1263 28.8631 21.249C30.5148 18.3988 30.137 14.8088 27.9287 12.3646L27.9278 12.3665ZM16.621 28.1696C15.3021 28.1714 14.0246 27.7099 13.0121 26.8648C13.0582 26.8403 13.1381 26.7962 13.1898 26.7642L19.1797 23.3049C19.4862 23.131 19.6742 22.8048 19.6723 22.4522V14.0078L22.2038 15.4696C22.2311 15.4828 22.249 15.5091 22.2527 15.5392V22.5321C22.249 25.6418 19.7306 28.163 16.621 28.1696ZM4.50945 22.9965C3.84863 21.8553 3.61081 20.5176 3.83735 19.2194C3.88154 19.2457 3.95954 19.2937 4.01501 19.3257L10.0049 22.785C10.3086 22.9627 10.6846 22.9627 10.9892 22.785L18.3018 18.5624V21.4859C18.3036 21.5159 18.2895 21.5451 18.266 21.5639L12.2112 25.0599C9.51423 26.6129 6.06995 25.6897 4.51042 22.9965H4.50945ZM2.93302 9.9215C3.59104 8.77841 4.62981 7.90416 5.8669 7.45014C5.8669 7.50182 5.86408 7.59303 5.86408 7.65695V14.5766C5.86218 14.9281 6.05019 15.2543 6.35572 15.4282L13.6683 19.65L11.1368 21.1117C11.1114 21.1287 11.0794 21.1315 11.0512 21.1193L4.99548 17.6204C2.30413 16.0618 1.38101 12.6185 2.93208 9.92243L2.93302 9.9215ZM23.7324 14.7618L16.4198 10.5391L18.9513 9.07829C18.9767 9.06136 19.0087 9.05853 19.0369 9.07077L25.0926 12.5668C27.7887 14.1244 28.7127 17.5734 27.155 20.2695C26.4961 21.4107 25.4583 22.2849 24.2221 22.7399V15.6134C24.2249 15.2619 24.0379 14.9366 23.7333 14.7618H23.7324ZM26.2517 10.9697C26.2075 10.9424 26.1295 10.8954 26.074 10.8634L20.0841 7.40406C19.7804 7.2264 19.4044 7.2264 19.0998 7.40406L11.7873 11.6267V8.70321C11.7854 8.67313 11.7995 8.64398 11.823 8.62518L17.8778 5.13199C20.5748 3.57621 24.0228 4.50217 25.5777 7.20008C26.2347 8.33941 26.4726 9.67333 26.2498 10.9697H26.2517ZM10.411 16.1803L7.87856 14.7185C7.85131 14.7054 7.83347 14.679 7.82971 14.649V7.65599C7.83157 4.54257 10.3575 2.01951 13.4709 2.02139C14.7879 2.02139 16.0626 2.48389 17.075 3.32618C17.0289 3.3506 16.95 3.39479 16.8973 3.42677L10.9074 6.88612C10.6009 7.06002 10.4129 7.38526 10.4148 7.73778L10.411 16.1784V16.1803ZM11.7863 13.2154L15.0436 11.3344L18.3008 13.2145V16.9756L15.0436 18.8556L11.7863 16.9756V13.2154Z" />
          </svg>
        ),
      },
      {
        id: 2,
        name: 'Company 2',
        logo: (
          <svg
            width="113"
            height="25"
            viewBox="0 0 113 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <path d="M0.75 2.69908C0.75 1.48458 1.73458 0.5 2.94908 0.5H11.551C12.7655 0.5 13.75 1.48458 13.75 2.69908V4.4005C13.75 5.00775 13.2577 5.50004 12.6505 5.50004H1.84954C1.24229 5.50004 0.75 5.00775 0.75 4.4005V2.69908Z" />
            <path d="M0.75 9.59954C0.75 8.99224 1.24229 8.5 1.84954 8.5H22.551C23.7655 8.5 24.7501 9.48453 24.7501 10.6991V16.4005C24.7501 17.0077 24.2578 17.5 23.6506 17.5H2.94908C1.73458 17.5 0.75 16.5154 0.75 15.3009V9.59954Z" />
            <path d="M11.75 21.5995C11.75 20.9923 12.2423 20.5 12.8495 20.5H23.6505C24.2577 20.5 24.75 20.9923 24.75 21.5995V22.3009C24.75 23.5154 23.7654 24.5 22.5509 24.5H13.9491C12.7346 24.5 11.75 23.5154 11.75 22.3009V21.5995Z" />
            <path d="M38.3455 15.241H40.6572L44.9499 22.2582H50.2059L45.4452 14.7456C48.0872 13.8375 49.628 11.8012 49.628 8.85671C49.628 4.72899 46.7389 2.5 42.0881 2.5H33.75V22.2582H38.3455V15.241ZM38.3455 11.4985V6.38009H41.8404C44.0144 6.38009 45.115 7.31571 45.115 8.93927C45.115 10.5353 44.0144 11.4985 41.8404 11.4985H38.3455Z" />
            <path d="M58.113 22.5607C61.0026 22.5607 63.6446 21.1023 64.7178 18.3229L61.0305 17.1396C60.6177 18.4055 59.5444 19.0659 58.1682 19.0659C56.4896 19.0659 55.3064 17.9377 55.0036 15.9563H64.8278V14.8006C64.8278 10.7002 62.4335 7.45312 58.0309 7.45312C53.8477 7.45312 50.7656 10.7002 50.7656 14.9932C50.7656 19.5062 53.7378 22.5607 58.113 22.5607ZM57.9757 10.8655C59.5991 10.8655 60.4525 11.9662 60.4799 13.2595H55.1413C55.5815 11.6635 56.6274 10.8655 57.9757 10.8655Z" />
            <path d="M67.3281 18.378C67.3281 21.35 68.8967 22.3957 72.0335 22.3957C73.0794 22.3957 73.9051 22.3133 74.6755 22.2031V18.5982C74.1801 18.6532 73.9324 18.6807 73.4097 18.6807C72.309 18.6807 71.6759 18.4606 71.6759 17.2498V11.3884H74.5103V7.75595H71.6759V3.73828H67.3281V7.75595H65.4844V11.3884H67.3281V18.378Z" />
            <path d="M90.4203 15.0207C90.4203 10.5077 87.4212 7.45312 83.1555 7.45312C78.8628 7.45312 75.8906 10.5077 75.8906 15.0207C75.8906 19.5337 78.8628 22.5607 83.1555 22.5607C87.4212 22.5607 90.4203 19.5337 90.4203 15.0207ZM80.2663 15.0207C80.2663 12.489 81.3943 11.0581 83.1555 11.0581C84.9171 11.0581 86.0451 12.489 86.0451 15.0207C86.0451 17.5524 84.9171 18.9833 83.1555 18.9833C81.3943 18.9833 80.2663 17.5524 80.2663 15.0207Z" />
            <path d="M106.28 15.0207C106.28 10.5077 103.281 7.45312 99.0148 7.45312C94.7222 7.45312 91.75 10.5077 91.75 15.0207C91.75 19.5337 94.7222 22.5607 99.0148 22.5607C103.281 22.5607 106.28 19.5337 106.28 15.0207ZM96.1257 15.0207C96.1257 12.489 97.2537 11.0581 99.0148 11.0581C100.776 11.0581 101.904 12.489 101.904 15.0207C101.904 17.5524 100.776 18.9833 99.0148 18.9833C97.2537 18.9833 96.1257 17.5524 96.1257 15.0207Z" />
            <path d="M112.747 2.5H108.344V22.2582H112.747V2.5Z" />
          </svg>
        ),
      },
      {
        id: 3,
        name: 'Company 3',
        logo: (
          <svg
            width="73"
            height="31"
            viewBox="0 0 73 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M72.2313 15.5944C72.2313 10.4676 69.7478 6.4222 65.0013 6.4222C60.2348 6.4222 57.3508 10.4676 57.3508 15.5543C57.3508 21.5824 60.7558 24.6264 65.6423 24.6264C68.0253 24.6264 69.8278 24.0857 71.1898 23.3247V19.3194C69.8278 20.0003 68.2658 20.4209 66.2833 20.4209C64.3403 20.4209 62.6183 19.74 62.3978 17.3768H72.1913C72.1913 17.1165 72.2313 16.0751 72.2313 15.5944ZM62.3378 13.6919C62.3378 11.4289 63.7198 10.4876 64.9813 10.4876C66.2028 10.4876 67.5048 11.4289 67.5048 13.6919H62.3378ZM49.6203 6.4222C47.6578 6.4222 46.3958 7.3434 45.6948 7.98425L45.4348 6.7426H41.0288V30.0938L46.0353 29.0323L46.0553 23.3648C46.7763 23.8855 47.8378 24.6264 49.6003 24.6264C53.1853 24.6264 56.4498 21.7426 56.4498 15.3941C56.4298 9.5864 53.1253 6.4222 49.6203 6.4222ZM48.4188 20.2206C47.2373 20.2206 46.5363 19.8001 46.0553 19.2794L46.0353 11.8494C46.5563 11.2687 47.2773 10.8681 48.4188 10.8681C50.2413 10.8681 51.5028 12.9108 51.5028 15.5343C51.5028 18.2179 50.2613 20.2206 48.4188 20.2206ZM34.1393 5.2406L39.1663 4.15915V0.09375L34.1393 1.15515V5.2406ZM34.1393 6.76265H39.1663V24.286H34.1393V6.76265ZM28.7518 8.2446L28.4313 6.76265H24.1053V24.286H29.1123V12.4102C30.2938 10.8681 32.2968 11.1485 32.9178 11.3688V6.76265C32.2768 6.5223 29.9333 6.08175 28.7518 8.2446ZM18.7383 2.41685L13.8513 3.45825L13.8313 19.4996C13.8313 22.4636 16.0543 24.6465 19.0188 24.6465C20.6608 24.6465 21.8623 24.3461 22.5233 23.9856V19.9202C21.8823 20.1805 18.7183 21.1017 18.7183 18.1378V11.0284H22.5233V6.76265H18.7183L18.7383 2.41685ZM5.19971 11.8494C5.19971 11.0684 5.84061 10.768 6.90206 10.768C8.42411 10.768 10.3468 11.2286 11.8688 12.0497V7.3434C10.2066 6.68255 8.56431 6.4222 6.90206 6.4222C2.83651 6.4222 0.132812 8.545 0.132812 12.0898C0.132812 17.6171 7.74321 16.736 7.74321 19.1191C7.74321 20.0403 6.94211 20.3407 5.82056 20.3407C4.15831 20.3407 2.03541 19.6598 0.353111 18.7386V23.505C2.21566 24.3061 4.09821 24.6465 5.82056 24.6465C9.98626 24.6465 12.8503 22.5837 12.8503 18.9989C12.8303 13.031 5.19971 14.0924 5.19971 11.8494Z"
            />
          </svg>
        ),
      },
      {
        id: 4,
        name: 'Company 4',
        logo: (
          <svg
            width="96"
            height="23"
            viewBox="0 0 96 23"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <path d="M53.6896 0.965339H59.5709L56.6122 22.0721H50.7313L53.6896 0.965339ZM46.2752 0.965339L42.3065 13.1242L40.5746 0.965339H36.4617L31.2658 13.0881L30.6168 0.965339H24.9158L26.9005 22.0721H31.6268L37.4717 8.72251L39.5286 22.0721H44.1827L51.8677 0.965339H46.2752ZM95.6509 13.2324H81.6879C81.76 15.9747 83.4016 17.7786 85.8191 17.7786C87.641 17.7786 89.0845 16.8044 90.203 14.9463L94.9167 17.0895C93.2968 20.2807 89.8833 22.3606 85.6749 22.3606C79.9382 22.3606 76.1315 18.5001 76.1315 12.2945C76.1315 5.47532 80.6054 0.640625 86.9194 0.640625C92.4758 0.640625 95.9754 4.39293 95.9754 10.2378C95.9754 11.2121 95.8672 12.1862 95.6509 13.2324ZM90.4194 9.19153C90.4194 6.73812 89.0484 5.18671 86.8473 5.18671C84.6466 5.18671 82.6979 6.81028 82.1932 9.19153H90.4194ZM6.53496 7.32622L0.53125 14.342H11.2506L12.4557 11.0334H7.8627L10.6697 7.78807L10.6787 7.70145L8.85309 4.56074H17.0631L10.6986 22.0721H15.0536L22.7386 0.965339H2.88366L6.53314 7.32622H6.53496ZM69.132 5.18671C71.2067 5.18671 73.0251 6.30155 74.6124 8.21378L75.446 2.26424C73.9665 1.26303 71.9643 0.640625 69.3123 0.640625C64.0448 0.640625 61.0861 3.72547 61.0861 7.64011C61.0861 10.3552 62.6017 12.0148 65.0912 13.0881L66.2819 13.6294C68.5004 14.5765 69.096 15.0455 69.096 16.0468C69.096 17.048 68.0947 17.7425 66.5704 17.7425C64.0535 17.7514 62.0153 16.4617 60.4818 14.2607L59.6322 20.324C61.3784 21.6555 63.6173 22.3606 66.5704 22.3606C71.5764 22.3606 74.6524 19.4743 74.6524 15.4694C74.6524 12.7454 73.4435 10.9956 70.3948 9.62454L69.096 9.01117C67.2919 8.20839 66.6786 7.76639 66.6786 6.88243C66.6786 5.92632 67.5174 5.18671 69.132 5.18671Z" />
          </svg>
        ),
      },
      {
        id: 5,
        name: 'Company 5',
        logo: (
          <svg
            width="99"
            height="31"
            viewBox="0 0 99 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <path d="M38.0781 26.5517V4.44531H42.0332V26.5517H38.0781Z" />
            <path d="M76.0156 11.5289H79.7927V13.3616C80.5953 11.8961 82.4697 11.1016 84.075 11.1016C86.0686 11.1016 87.6738 11.9875 88.4172 13.6052C89.5763 11.7725 91.124 11.1016 93.0571 11.1016C95.763 11.1016 98.3503 12.7811 98.3503 16.811V26.5515H94.5139V17.6361C94.5139 16.0175 93.7402 14.7965 91.926 14.7965C90.2305 14.7965 89.2189 16.1402 89.2189 17.7588V26.5525H85.2946V17.6361C85.2946 16.0175 84.4919 14.7965 82.7067 14.7965C80.9813 14.7965 79.9698 16.1097 79.9698 17.7588V26.5525H76.0156V11.5289Z" />
            <path d="M50.971 26.9864C46.5155 26.9864 43.2891 23.5951 43.2891 19.0402C43.2891 14.5579 46.5052 11.0859 50.971 11.0859C55.459 11.0859 58.6533 14.5901 58.6533 19.0402C58.6533 23.562 55.425 26.9864 50.971 26.9864ZM50.971 14.763C48.6732 14.763 46.8038 16.6816 46.8038 19.0412C46.8038 21.4006 48.6732 23.3193 50.971 23.3193C53.2691 23.3193 55.1376 21.4006 55.1376 19.0412C55.1376 16.6816 53.2691 14.763 50.971 14.763Z" />
            <path d="M67.1041 26.9864C62.6483 26.9864 59.4219 23.5951 59.4219 19.0402C59.4219 14.5579 62.638 11.0859 67.1041 11.0859C71.5918 11.0859 74.7861 14.5901 74.7861 19.0402C74.7861 23.562 71.5561 26.9864 67.1041 26.9864ZM67.1041 14.7119C64.7797 14.7119 62.8885 16.6539 62.8885 19.0393C62.8885 21.4248 64.7797 23.3667 67.1041 23.3667C69.4282 23.3667 71.3194 21.4248 71.3194 19.0393C71.3185 16.6539 69.4273 14.7119 67.1041 14.7119Z" />
            <path d="M30.1043 13.8198H21.4933L28.9509 9.40149L27.3131 6.48975L19.8556 10.9081L24.1602 3.25587L21.3228 1.57426L17.0181 9.22646V0.390625H13.7425V9.22731L9.43615 1.57426L6.59959 3.25502L10.9051 10.9072L3.44749 6.48975L1.80966 9.40064L9.26724 13.819H0.65625V17.1805H9.26641L1.80966 21.5989L3.44749 24.5106L10.9043 20.0931L6.59876 27.7453L9.43615 29.426L13.7416 21.7731V30.6097H17.0173V21.7739L21.322 29.426L24.1593 27.7453L19.8539 20.0923L27.3114 24.5106L28.9493 21.5989L21.4925 17.1813H30.1026V13.8198H30.1043ZM15.3803 20.0719C12.9104 20.0719 10.9084 18.0176 10.9084 15.4828C10.9084 12.9482 12.9104 10.8937 15.3803 10.8937C17.8502 10.8937 19.8521 12.9482 19.8521 15.4828C19.8521 18.0176 17.8502 20.0719 15.3803 20.0719Z" />
          </svg>
        ),
      },
      {
        id: 6,
        name: 'Company 6',
        logo: (
          <svg
            width="132"
            height="21"
            viewBox="0 0 132 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <path d="M62.1915 1.56344L62.1641 1.56944C61.1444 1.79968 60.627 2.14298 60.627 3.3812H60.6224L60.627 18.2354C60.714 19.2134 61.2396 19.5153 62.1641 19.7239L62.1915 19.7302V19.9404H55.0749V19.7302L55.1023 19.7239C56.0258 19.5153 56.5392 19.2134 56.6262 18.2354V3.78459L49.767 19.907H49.3889L42.7236 4.22197V17.8774C42.7236 19.1156 43.2415 19.4589 44.2617 19.6892L44.2886 19.6952V19.907H40.2031V19.6945L40.2299 19.6884C41.25 19.4583 41.7701 19.1149 41.7701 17.8767V3.3812C41.7701 2.14298 41.2521 1.79968 40.2319 1.56944L40.2051 1.56344V1.35156H46.2267L51.415 13.5466L56.6038 1.35156H62.1915V1.56344Z" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M73.6869 11.9082H65.346L65.345 11.9089C65.1832 14.7676 66.9661 17.1276 69.2933 17.3017C70.9984 17.4292 72.4968 16.3842 73.3307 14.6598L73.5704 14.7442C73.0957 17.9527 71.1917 20.343 67.9221 20.343C64.1966 20.343 62.0993 17.24 62.0093 13.3573C62.0067 13.2072 62.0077 13.058 62.0108 12.9098C62.0128 12.8174 62.0164 12.7253 62.021 12.6336C62.0235 12.5845 62.026 12.5385 62.0296 12.4871C62.0332 12.4357 62.0367 12.3843 62.0413 12.3329C62.1751 10.6186 62.7272 9.09318 63.6379 7.96441C64.2037 7.26377 64.884 6.72991 65.6549 6.37292C66.3321 6.03191 67.2841 5.84375 68.06 5.84375H68.0936C69.589 5.84375 71.0126 6.34957 71.9742 7.26875C73.0865 8.33244 73.6635 9.8816 73.6869 11.8732V11.9082ZM68.0376 6.65751C66.4679 6.68987 65.5327 8.56971 65.4162 11.1742H70.1802C70.229 9.14122 69.9141 7.78093 69.243 7.12995C68.9275 6.82435 68.5215 6.66555 68.0376 6.65751Z"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M87.1166 17.8132C87.1166 19.1145 87.6346 19.4738 88.6542 19.7151L88.6812 19.7204V19.9426H83.121V17.7972C82.4844 19.3954 81.0928 20.343 79.3634 20.343C76.4077 20.343 74.4214 17.6311 74.4219 13.5962C74.4219 11.337 75.0711 9.38915 76.2999 7.96379C77.4788 6.59618 79.1136 5.84313 80.9015 5.84313C81.7833 5.84313 82.5104 6.03297 83.121 6.42298V4.04938C83.121 3.52357 82.9927 3.14984 82.7292 2.90632C82.3923 2.59532 81.8128 2.47723 80.958 2.54627L80.9198 2.54928V2.32575L87.1166 0.5V17.8132ZM81.5116 18.1759C82.2768 18.1759 82.8477 17.8189 83.121 17.17V7.60945C82.8508 6.98655 82.2845 6.62951 81.5645 6.62951C79.8463 6.62951 78.7355 8.93874 78.7355 12.5088C78.7355 14.2241 79.0164 15.6722 79.5507 16.6965C80.0472 17.6508 80.7463 18.1759 81.5116 18.1759Z"
            />
            <path d="M91.7591 0.644531C93.0189 0.644531 93.9689 1.59481 93.9689 2.85465C93.9689 4.11454 93.0189 5.06476 91.7591 5.06476C90.5059 5.06476 89.5234 4.09383 89.5234 2.85465C89.5234 1.61546 90.5049 0.644531 91.7591 0.644531Z" />
            <path d="M93.8929 17.8119C93.8929 19.1132 94.4104 19.4725 95.4306 19.7137L95.4591 19.7191V19.9413H89.8937V9.62677C89.8937 8.22074 89.5645 7.80631 88.3612 7.69925L88.3281 7.69625V7.47934L93.8884 5.88281V17.8119H93.8929Z" />
            <path d="M108.142 17.8119C108.142 19.1132 108.66 19.4725 109.68 19.7137L109.706 19.7227V19.9449H104.146V17.7429C103.514 19.3284 102.079 20.3417 100.442 20.3417C98.5098 20.3417 97.2764 19.4595 96.8053 17.7725L96.7056 17.3371C96.6257 16.8988 96.587 16.454 96.5895 16.0085V9.62677C96.5895 8.22039 96.2598 7.806 95.057 7.69925L95.0234 7.69625V7.4797L100.584 5.88317V15.9034C100.584 17.3858 101.25 18.2023 102.459 18.2023C103.204 18.1763 103.819 17.7909 104.147 17.1419V9.60138C104.147 8.18904 103.863 7.75095 102.879 7.64751L102.848 7.6442V7.42663L108.142 5.88281V17.8119Z" />
            <path d="M131.286 19.7207V19.9432H125.726V10.0431C125.726 8.44255 125.157 7.66482 123.984 7.66482C123.016 7.66482 122.518 8.36649 122.271 8.95936C122.321 9.26455 122.346 9.57356 122.344 9.88292V18.1575C122.344 19.1285 122.629 19.5068 123.512 19.7144L123.539 19.721V19.9432H118.35V10.0431C118.35 8.4656 117.764 7.66482 116.608 7.66482C115.677 7.66482 115.206 8.31107 114.973 8.85663V18.1575C114.973 19.1285 115.258 19.5068 116.141 19.7144L116.168 19.721V19.9432H110.98V9.53993C110.98 8.16998 110.636 7.74353 109.448 7.63811L109.414 7.63511V7.41692L114.975 5.88578V8.21699C115.537 6.72625 116.847 5.84375 118.52 5.84375C120.424 5.84375 121.711 6.75327 122.158 8.40821C122.721 6.84638 124.185 5.84375 125.92 5.84375C128.3 5.84375 129.721 7.35383 129.721 9.88394V17.9984C129.721 18.978 130.152 19.4591 131.259 19.7144L131.286 19.7207Z" />
            <path d="M10.0364 20.5015C15.4631 20.5015 19.8622 16.0719 19.8622 10.608C19.8622 5.14408 15.4628 0.714844 10.0364 0.714844C4.61003 0.714844 0.210938 5.14276 0.210938 10.608C0.210938 16.0732 4.60973 20.5015 10.0364 20.5015Z" />
            <path d="M25.7254 19.9227C28.4386 19.9227 30.6383 15.7534 30.6383 10.6098C30.6383 5.46753 28.4386 1.29688 25.7254 1.29688C23.0122 1.29688 20.8125 5.46753 20.8125 10.6098C20.8125 15.7521 23.0122 19.9227 25.7254 19.9227Z" />
            <path d="M35.0496 10.6057C35.0496 15.2142 34.2759 18.9497 33.3217 18.9497C32.3675 18.9497 31.5938 15.2128 31.5938 10.6057C31.5938 5.99865 32.3675 2.26172 33.322 2.26172C34.2766 2.26172 35.0496 5.99763 35.0496 10.6057Z" />
          </svg>
        ),
      },
      {
        id: 7,
        name: 'Company 7',
        logo: (
          <svg
            width="134"
            height="31"
            viewBox="0 0 134 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <mask
              id="mask0_342_6736"
              maskUnits="userSpaceOnUse"
              x="42"
              y="7"
              width="92"
              height="20"
            >
              <path
                d="M133.02 7.77734H42.4375V26.5404H133.02V7.77734Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask0_342_6736)">
              <path d="M53.9534 12.179C53.8381 12.2504 53.7006 12.2758 53.5672 12.2501C53.4343 12.2243 53.3155 12.1494 53.2353 12.0401C52.8379 11.4719 52.3053 11.0113 51.6861 10.6998C51.0663 10.3882 50.3791 10.2355 49.6856 10.2552C46.9763 10.2552 45.3095 12.3991 45.3095 15.4608C45.3095 18.5225 47.0033 20.707 49.7131 20.707C50.4172 20.7231 51.1137 20.5597 51.737 20.232C52.3603 19.9043 52.89 19.4232 53.2763 18.8341C53.3535 18.723 53.47 18.645 53.6023 18.6155C53.7345 18.586 53.8733 18.6072 53.9909 18.6749L55.2744 19.4199C55.34 19.4582 55.3973 19.5096 55.4418 19.5711C55.4863 19.6326 55.5179 19.7028 55.5337 19.7771C55.5495 19.8514 55.5501 19.9282 55.5343 20.0026C55.5191 20.077 55.488 20.1473 55.4436 20.2091C54.8144 21.1522 53.9564 21.9201 52.9491 22.4407C51.9418 22.9614 50.8193 23.2179 49.6856 23.1861C45.3738 23.1861 42.4375 20.0432 42.4375 15.4608C42.4375 10.8784 45.3738 7.77607 49.6417 7.77607C50.7561 7.74511 51.8611 7.98763 52.8601 8.4824C53.8592 8.97716 54.7219 9.70913 55.3727 10.6142C55.4172 10.6749 55.4488 10.7441 55.4658 10.8176C55.4822 10.8911 55.4839 10.9672 55.4699 11.0413C55.4558 11.1153 55.4266 11.1856 55.3844 11.2479C55.3423 11.3102 55.2873 11.3631 55.2235 11.4033L53.9534 12.179Z" />
              <path d="M63.4416 22.3742V21.751C62.7638 22.6249 61.8157 23.1668 60.2951 23.1668C58.0459 23.1668 56.4844 21.9373 56.4844 19.7799C56.4844 17.0942 58.8793 16.6336 60.3595 16.427C61.9409 16.1967 63.2958 16.0883 63.2958 15.0519C63.2958 14.1341 62.2324 13.8868 61.3346 13.8868C60.3056 13.9043 59.2943 14.1606 58.3813 14.6353C58.3175 14.6703 58.2472 14.692 58.1752 14.699C58.1027 14.7062 58.0301 14.6986 57.9604 14.6767C57.8914 14.6549 57.827 14.6193 57.772 14.572C57.717 14.5248 57.6719 14.4668 57.6397 14.4016L57.1651 13.4398C57.1024 13.3144 57.0907 13.1696 57.1323 13.0357C57.1738 12.9018 57.2657 12.7893 57.3886 12.7218C58.6657 12.0363 60.092 11.6757 61.5412 11.6719C64.1223 11.6719 65.9442 12.7319 65.9442 15.2958V22.3742C65.9442 22.518 65.8875 22.6558 65.7856 22.7574C65.6844 22.859 65.5462 22.9161 65.4028 22.9161H63.98C63.8366 22.9152 63.6997 22.8577 63.599 22.7562C63.4983 22.6547 63.4416 22.5174 63.4416 22.3742ZM63.2958 17.4802C62.7948 17.8562 61.8765 17.9815 60.898 18.1577C59.9188 18.3338 59.1093 18.5945 59.1093 19.658C59.1093 20.6368 59.8175 21.094 60.8371 21.094C62.1071 21.094 63.2958 20.3861 63.2958 18.7401V17.4802Z" />
              <path d="M68.2854 20.1116C68.3767 20.0042 68.5061 19.9367 68.6465 19.9234C68.787 19.9102 68.9269 19.9522 69.0369 20.0404C69.9412 20.7245 71.0503 21.0826 72.1834 21.0565C73.3522 21.0565 74.2161 20.6806 74.2161 19.7661C74.2161 18.933 73.423 18.7501 71.5062 18.4317C69.4741 18.093 67.5263 17.4157 67.5263 15.0991C67.5263 12.6843 69.6093 11.6648 71.9838 11.6648C73.4728 11.6423 74.9266 12.1193 76.1124 13.0195C76.1709 13.0646 76.2195 13.1211 76.2546 13.1856C76.2903 13.2501 76.3125 13.3212 76.3195 13.3946C76.3266 13.4679 76.3184 13.5419 76.2956 13.612C76.2727 13.682 76.2359 13.7467 76.1873 13.8019L75.5095 14.5978C75.42 14.7046 75.2924 14.7728 75.1537 14.7886C75.0155 14.8043 74.8763 14.7664 74.7645 14.6825C73.9375 14.074 72.9361 13.7486 71.9095 13.7545C70.8934 13.7545 70.036 14.0458 70.036 14.8349C70.036 15.7121 71.2657 15.8781 72.4953 16.0846C75.1168 16.5452 76.7831 17.2938 76.7831 19.4714C76.7831 21.7644 74.9506 23.18 72.0412 23.18C70.4504 23.2285 68.893 22.7193 67.6381 21.7406C67.5819 21.6948 67.5357 21.6382 67.5012 21.5742C67.4672 21.5102 67.4461 21.44 67.4397 21.3678C67.4333 21.2955 67.4409 21.2227 67.4631 21.1536C67.4848 21.0844 67.5199 21.0203 67.5673 20.965L68.2854 20.1116Z" />
              <path d="M78.3594 22.3732V8.58875C78.3594 8.44502 78.4162 8.30719 78.518 8.20559C78.6198 8.10394 78.7574 8.04688 78.9013 8.04688H80.4629C80.6063 8.04688 80.7444 8.10394 80.8456 8.20559C80.9475 8.30719 81.0042 8.44502 81.0042 8.58875V13.2931C81.3923 12.7737 81.8997 12.3552 82.4832 12.0729C83.0668 11.7906 83.7094 11.6526 84.3579 11.6708C86.6709 11.6708 88.0223 13.3168 88.0223 15.6672V22.3732C88.0223 22.5168 87.9655 22.6547 87.8637 22.7563C87.7619 22.8579 87.6243 22.915 87.4804 22.915H85.9188C85.7754 22.915 85.6373 22.8579 85.5361 22.7563C85.4342 22.6547 85.3774 22.5168 85.3774 22.3732V16.2769C85.3774 15.1084 85.0046 14.0483 83.504 14.0483C82.0882 14.0483 81.0042 15.0474 81.0042 16.7137V22.3732C81.0042 22.5168 80.9475 22.6547 80.8456 22.7563C80.7444 22.8579 80.6063 22.915 80.4629 22.915H78.9013C78.7574 22.915 78.6198 22.8579 78.518 22.7563C78.4162 22.6547 78.3594 22.5168 78.3594 22.3732Z" />
              <path d="M105.217 22.5479L104.086 19.2322H98.409L97.2847 22.5479C97.2484 22.6557 97.1793 22.7493 97.0868 22.8156C96.9944 22.8819 96.8832 22.9173 96.7696 22.9171H94.9406C94.8534 22.9172 94.7668 22.896 94.6895 22.8555C94.6117 22.815 94.545 22.7562 94.4952 22.6843C94.4455 22.6124 94.4133 22.5295 94.4027 22.4427C94.3916 22.3558 94.4022 22.2676 94.4326 22.1856L99.5474 8.40121C99.5877 8.30026 99.658 8.21394 99.7487 8.15348C99.8394 8.09308 99.9465 8.06136 100.055 8.06253H102.571C102.68 8.06136 102.787 8.09308 102.878 8.15348C102.969 8.21394 103.039 8.30026 103.08 8.40121L108.177 22.189C108.207 22.2708 108.217 22.3586 108.206 22.445C108.194 22.5314 108.163 22.6138 108.113 22.6853C108.063 22.7568 107.996 22.8151 107.919 22.8555C107.842 22.8959 107.756 22.917 107.669 22.9171H105.732C105.618 22.9173 105.507 22.8819 105.415 22.8156C105.322 22.7493 105.253 22.6557 105.217 22.5479ZM101.282 10.4638L99.0967 16.9225H103.384L101.282 10.4638Z" />
              <path d="M112.082 12.464V13.3174C112.467 12.7842 112.977 12.3546 113.568 12.0673C114.16 11.78 114.813 11.644 115.469 11.6714C118.471 11.6714 120.408 14.1506 120.408 17.3986C120.408 20.6465 118.471 23.1562 115.469 23.1562C114.811 23.1811 114.157 23.0418 113.565 22.751C112.974 22.46 112.465 22.0267 112.082 21.4899V25.9977C112.082 26.1414 112.026 26.2793 111.924 26.3809C111.822 26.4825 111.684 26.5396 111.54 26.5396H109.979C109.835 26.5396 109.697 26.4825 109.596 26.3809C109.494 26.2793 109.438 26.1414 109.438 25.9977V12.4504C109.438 12.3067 109.494 12.1689 109.596 12.0672C109.697 11.9656 109.835 11.9085 109.979 11.9085H111.527C111.601 11.9067 111.673 11.9198 111.742 11.947C111.81 11.9743 111.872 12.0151 111.924 12.0671C111.976 12.119 112.017 12.181 112.044 12.2492C112.071 12.3175 112.084 12.3905 112.082 12.464ZM114.914 20.8531C116.746 20.8531 117.644 19.2918 117.644 17.3986C117.644 15.5054 116.746 13.9813 114.914 13.9813C113.081 13.9813 112.082 15.502 112.082 17.3986C112.082 19.2952 113.062 20.8531 114.914 20.8531Z" />
              <path d="M124.689 12.4637V13.3172C125.073 12.7846 125.584 12.3555 126.175 12.0683C126.766 11.7811 127.419 11.6447 128.076 11.6712C131.073 11.6712 133.01 14.1504 133.01 17.3984C133.01 20.6463 131.073 23.1559 128.076 23.1559C127.417 23.1799 126.763 23.0403 126.172 22.7495C125.581 22.4587 125.071 22.0258 124.689 21.4896V25.9974C124.689 26.1412 124.631 26.279 124.53 26.3807C124.428 26.4823 124.29 26.5394 124.147 26.5394H122.589C122.445 26.5394 122.307 26.4823 122.206 26.3807C122.104 26.279 122.047 26.1412 122.047 25.9974V12.4502C122.046 12.3789 122.06 12.3082 122.087 12.2423C122.114 12.1763 122.154 12.1164 122.204 12.066C122.255 12.0156 122.315 11.9757 122.38 11.9486C122.447 11.9215 122.517 11.9078 122.589 11.9083H124.147C124.219 11.9078 124.291 11.9219 124.358 11.9499C124.425 11.9778 124.485 12.0189 124.536 12.0707C124.586 12.1226 124.626 12.1841 124.652 12.2517C124.678 12.3192 124.691 12.3913 124.689 12.4637ZM127.523 20.8529C129.356 20.8529 130.233 19.2916 130.233 17.3984C130.233 15.5051 129.339 13.981 127.523 13.981C125.708 13.981 124.689 15.5017 124.689 17.3984C124.689 19.295 125.671 20.8529 127.523 20.8529Z" />
            </g>
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M26.0362 1.36815C24.4417 0.867188 22.9893 0.867188 20.0373 0.867188H11.1812C8.24598 0.867188 6.77676 0.867188 5.20932 1.35464C4.35365 1.66446 3.57651 2.15864 2.93294 2.80218C2.28943 3.44565 1.79528 4.22274 1.48543 5.07842C0.984375 6.66273 0.984375 8.12853 0.984375 11.0635V19.9194C0.984375 22.8713 0.984375 24.3236 1.47185 25.9045C1.7817 26.7602 2.27591 27.5373 2.91942 28.1808C3.56294 28.8243 4.34007 29.3184 5.19581 29.6283C6.78015 30.1293 8.24598 30.1293 11.1812 30.1293H20.0508C22.9893 30.1293 24.4552 30.1293 26.0362 29.6283C26.8919 29.3184 27.669 28.8243 28.3125 28.1808C28.9561 27.5373 29.4503 26.7602 29.7601 25.9045C30.2612 24.3202 30.2612 22.8544 30.2612 19.9194V11.0771C30.2612 8.13871 30.2612 6.67286 29.7601 5.09194C29.4503 4.23631 28.9561 3.45917 28.3125 2.8157C27.669 2.17216 26.8919 1.67804 26.0362 1.36815ZM20.2896 11.8979C19.1918 10.9688 17.8008 10.458 16.3626 10.4558C15.1777 10.4558 13.9928 10.8485 13.9928 11.9385C13.9928 12.9322 15.0477 13.321 16.3473 13.8C16.4733 13.8465 16.6017 13.8938 16.7316 13.9426C19.3011 14.8092 21.4169 15.8721 21.4169 18.3908C21.4169 21.1261 19.2943 22.9947 15.8209 23.208L15.5061 24.6738C15.4773 24.8067 15.4038 24.9256 15.2978 25.0106C15.1917 25.0957 15.0597 25.1417 14.9238 25.141H12.7402C12.652 25.1389 12.5653 25.1173 12.4863 25.078C12.4072 25.0387 12.3378 24.9825 12.2829 24.9134C12.228 24.8443 12.189 24.764 12.1686 24.6781C12.1481 24.5922 12.1468 24.5029 12.1647 24.4166L12.5032 22.8695C11.1922 22.5352 9.98689 21.8749 8.99935 20.95C8.94275 20.8954 8.89769 20.83 8.86696 20.7575C8.83617 20.6851 8.82031 20.6073 8.82031 20.5286C8.82031 20.4499 8.83617 20.3721 8.86696 20.2996C8.89769 20.2272 8.94275 20.1617 8.99935 20.1071L10.2113 18.9223C10.3215 18.8139 10.4698 18.7532 10.6243 18.7532C10.7788 18.7532 10.9272 18.8139 11.0373 18.9223C12.1476 19.9815 13.6299 20.5615 15.1641 20.537C16.7451 20.537 17.8183 19.8668 17.8183 18.8038C17.8183 17.8428 16.9383 17.5209 15.2686 16.9102C15.0914 16.8454 14.9053 16.7773 14.7105 16.7049C12.5642 15.9398 10.533 14.8464 10.533 12.3041C10.533 9.36229 12.9907 7.92699 15.8886 7.78477L16.1933 6.29187C16.2226 6.16025 16.2959 6.0425 16.4012 5.95817C16.5064 5.87384 16.6373 5.82795 16.7722 5.82813H18.949C19.0366 5.82784 19.1231 5.84709 19.2023 5.88443C19.2815 5.92182 19.3513 5.97637 19.4068 6.04414C19.4623 6.11191 19.5019 6.19115 19.5228 6.27619C19.5438 6.36123 19.5455 6.44983 19.5279 6.53563L19.1893 8.20457C20.2976 8.56683 21.3244 9.14178 22.2125 9.89721C22.2731 9.94923 22.3222 10.0133 22.3568 10.0852C22.3914 10.1572 22.4107 10.2355 22.4136 10.3154C22.4164 10.3952 22.4028 10.4747 22.3734 10.549C22.344 10.6232 22.2996 10.6906 22.243 10.7469L21.1123 11.8809C21.0032 11.9882 20.8573 12.0498 20.7043 12.0529C20.5513 12.0561 20.403 12.0006 20.2896 11.8979Z"
            />
          </svg>
        ),
      },
      {
        id: 8,
        name: 'Company 8',
        logo: (
          <svg
            width="122"
            height="31"
            viewBox="0 0 122 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="dark:fill-white fill-black"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M4.65498 5.4192C4.49517 5.59338 4.50576 5.86062 4.67364 6.02715L25.4235 26.6055C25.5914 26.772 25.8609 26.7825 26.0365 26.624C29.079 23.8784 30.9888 19.9197 30.9888 15.5187C30.9888 7.22841 24.2123 0.507812 15.8529 0.507812C11.4152 0.507812 7.42352 2.40182 4.65498 5.4192ZM2.06157 9.32463C1.98489 9.4923 2.02355 9.68889 2.15481 9.81906L21.6 29.1035C21.7313 29.2337 21.9295 29.2721 22.0986 29.196C22.5478 28.994 22.9852 28.7708 23.4097 28.5277C23.662 28.3833 23.7007 28.0419 23.4946 27.8376L3.43132 7.94007C3.22533 7.73577 2.88109 7.77411 2.73544 8.02431C2.49037 8.44527 2.26532 8.87913 2.06157 9.32463ZM0.88189 14.8466C0.790844 14.7563 0.742401 14.6319 0.750973 14.5045C0.791115 13.9077 0.866419 13.3206 0.974847 12.745C1.03986 12.3998 1.46512 12.2801 1.71546 12.5283L18.8682 29.5393C19.1185 29.7875 18.9977 30.2093 18.6497 30.2738C18.0693 30.3813 17.4773 30.456 16.8756 30.4958C16.7471 30.5043 16.6216 30.4562 16.5306 30.3659L0.88189 14.8466ZM1.93451 19.0355C1.622 18.7256 1.11767 18.9926 1.23213 19.416C2.62655 24.5739 6.7222 28.6357 11.9231 30.0186C12.35 30.1321 12.6193 29.632 12.3068 29.322L1.93451 19.0355ZM61.7293 8.76831C62.8144 8.76831 63.6937 7.89291 63.6937 6.81306C63.6937 5.73321 62.8144 4.85781 61.7293 4.85781C60.6445 4.85781 59.7652 5.73321 59.7652 6.81306C59.7652 7.89291 60.6445 8.76831 61.7293 8.76831ZM44.9039 25.8202V4.85898H48.3948V22.7534H57.8071V25.8202H44.9039ZM69.7501 17.2669V25.8202H66.3724V10.9645H69.7077V13.5107L69.7501 13.4826C70.0892 12.6854 70.6358 12.0196 71.3896 11.485C72.1432 10.941 73.1042 10.669 74.2728 10.669C75.3091 10.669 76.2511 10.8988 77.0993 11.3584C77.9472 11.8085 78.6254 12.4697 79.1342 13.3419C79.643 14.2142 79.8974 15.2833 79.8974 16.5494V25.8202H76.5197V17.0137C76.5197 15.8882 76.2181 15.0348 75.6152 14.4533C75.0217 13.8625 74.2256 13.567 73.2267 13.567C72.586 13.567 72.0019 13.6983 71.4743 13.9609C70.9468 14.2235 70.5275 14.6268 70.2165 15.1708C69.9056 15.7147 69.7501 16.4134 69.7501 17.2669ZM100.334 25.567C101.107 25.8952 101.993 26.0594 102.991 26.0594C103.811 26.0594 104.513 25.9562 105.097 25.7498C105.681 25.5341 106.162 25.2481 106.539 24.8917C106.925 24.5353 107.231 24.1461 107.457 23.7241H107.514V25.8202H110.75V15.5928C110.75 14.8706 110.609 14.2095 110.326 13.6092C110.044 13.009 109.634 12.4885 109.097 12.0477C108.569 11.6069 107.928 11.2693 107.174 11.0348C106.421 10.7909 105.573 10.669 104.631 10.669C103.34 10.669 102.223 10.8894 101.281 11.3302C100.348 11.7617 99.6183 12.3431 99.0905 13.0747C98.5629 13.8062 98.2755 14.6315 98.2283 15.5506H101.493C101.531 15.1192 101.682 14.7347 101.945 14.397C102.209 14.0594 102.567 13.7968 103.02 13.6092C103.472 13.4123 103.995 13.3138 104.588 13.3138C105.182 13.3138 105.686 13.4123 106.101 13.6092C106.524 13.8062 106.85 14.0735 107.076 14.4111C107.302 14.7487 107.415 15.1426 107.415 15.5928V15.7054C107.415 16.043 107.297 16.2915 107.062 16.451C106.835 16.6104 106.449 16.7276 105.903 16.8026C105.366 16.8777 104.631 16.9668 103.698 17.0699C102.935 17.1543 102.2 17.281 101.493 17.4498C100.787 17.6186 100.155 17.8671 99.5993 18.1954C99.0529 18.5236 98.6195 18.9597 98.2991 19.5037C97.9788 20.0477 97.8188 20.7464 97.8188 21.5998C97.8188 22.5846 98.0447 23.4099 98.497 24.0758C98.9492 24.7323 99.5618 25.2293 100.334 25.567ZM105.804 23.0769C105.257 23.3677 104.584 23.513 103.783 23.513C102.972 23.513 102.327 23.3442 101.847 23.0066C101.366 22.6596 101.126 22.186 101.126 21.5858C101.126 21.1168 101.258 20.737 101.521 20.4462C101.795 20.1555 102.153 19.9257 102.596 19.7569C103.038 19.5881 103.519 19.4709 104.037 19.4052C104.414 19.349 104.781 19.2927 105.14 19.2364C105.497 19.1708 105.832 19.1098 106.143 19.0535C106.454 18.9879 106.718 18.9222 106.934 18.8566C107.161 18.7909 107.325 18.7206 107.429 18.6455V20.3056C107.429 20.887 107.292 21.4216 107.019 21.9093C106.755 22.3876 106.35 22.7768 105.804 23.0769ZM113.697 25.8202V10.9645H116.948V13.4123H116.99C117.263 12.5682 117.692 11.9258 118.276 11.485C118.87 11.0348 119.647 10.8097 120.608 10.8097C120.844 10.8097 121.056 10.8191 121.244 10.8379C121.442 10.8472 121.607 10.8566 121.739 10.866V13.8906C121.616 13.8718 121.4 13.8484 121.089 13.8203C120.778 13.7921 120.448 13.778 120.099 13.778C119.543 13.778 119.035 13.9046 118.573 14.1579C118.111 14.4111 117.744 14.8003 117.471 15.3255C117.207 15.8414 117.075 16.4932 117.075 17.281V25.8202H113.697ZM60.0335 25.8202V10.9645H63.4112V25.8202H60.0335ZM85.5155 25.159C86.599 25.8249 87.8853 26.1578 89.3739 26.1578C90.5234 26.1578 91.5691 25.9515 92.5114 25.5388C93.4631 25.1168 94.2544 24.54 94.8857 23.8085C95.517 23.0676 95.9269 22.2141 96.1154 21.2481H92.9355C92.7939 21.6889 92.5631 22.0781 92.2428 22.4157C91.9321 22.744 91.541 23.0019 91.07 23.1895C90.5987 23.3771 90.0524 23.4708 89.4304 23.4708C88.5919 23.4708 87.871 23.2833 87.2682 22.9081C86.6747 22.533 86.2224 22.0125 85.9114 21.3466C85.6292 20.7337 85.4749 20.0397 85.4489 19.2645H96.3132V18.3642C96.3132 17.22 96.1435 16.179 95.8044 15.2411C95.4653 14.2939 94.9846 13.4779 94.3627 12.7933C93.741 12.0993 92.9921 11.5647 92.1157 11.1896C91.2488 10.8144 90.2832 10.6268 89.2184 10.6268C87.8335 10.6268 86.6087 10.9598 85.5439 11.6257C84.4791 12.2915 83.6454 13.2107 83.0422 14.383C82.4394 15.5553 82.1378 16.8964 82.1378 18.4064C82.1378 19.907 82.43 21.2434 83.0141 22.4157C83.5982 23.5787 84.4322 24.4931 85.5155 25.159ZM92.6245 15.3255C92.3135 14.6878 91.8707 14.1954 91.2959 13.8484C90.7212 13.5014 90.043 13.3279 89.2607 13.3279C88.4881 13.3279 87.8145 13.5014 87.2397 13.8484C86.6746 14.1954 86.2318 14.6878 85.9114 15.3255C85.6828 15.7874 85.5382 16.3126 85.4777 16.9011H93.058C92.9975 16.3126 92.8529 15.7874 92.6245 15.3255Z"
            />
          </svg>
        ),
      },
    ],
  },
  featureSection: {
    title: 'How  Atlas Works',
    description:
      'Discover how  Atlas transforms your commands into action in four easy steps',
    items: [
      {
        id: 1,
        title: 'Request an Action',
        content:
          'Speak or type your command—let  Atlas capture your intent. Your request instantly sets the process in motion.',
        image:
          'https://images.unsplash.com/photo-1720371300677-ba4838fa0678?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
      {
        id: 2,
        title: 'AI Understanding & Planning',
        content:
          'Atlas analyzes your request, understands the context, and develops a structured plan to complete the task efficiently.',
        image:
          'https://images.unsplash.com/photo-1686170287433-c95faf6d3608?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwzfHx8ZW58MHx8fHx8fA%3D%3D',
      },
      {
        id: 3,
        title: 'Autonomous Execution',
        content:
          'Using its capabilities and integrations, Atlas executes the task independently, handling any complexities along the way.',
        image:
          'https://images.unsplash.com/photo-1720378042271-60aff1e1c538?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwxMHx8fGVufDB8fHx8fA%3D%3D',
      },
      {
        id: 4,
        title: 'Results & Learning',
        content:
          'Atlas delivers results and learns from each interaction, continuously improving its performance to better serve your needs.',
        image:
          'https://images.unsplash.com/photo-1666882990322-e7f3b8df4f75?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDF8fHxlbnwwfHx8fHw%3D',
      },
    ],
  },
  bentoSection: {
    title: 'Empower Your Workflow with  Atlas',
    description:
      'Let  Atlas act on your behalf with advanced AI capabilities, seamless integrations, and autonomous task execution.',
    items: [
      {
        id: 1,
        content: <FirstBentoAnimation />,
        title: 'Autonomous Task Execution',
        description:
          'Experience true automation with Atlas. Ask your AI Agent to complete tasks, research information, and handle complex workflows with minimal supervision.',
      },
      {
        id: 2,
        content: <SecondBentoAnimation />,
        title: 'Seamless Integrations',
        description:
          'Connect Atlas to your existing tools for a unified workflow. Boost productivity through AI-powered interconnected systems.',
      },
      {
        id: 3,
        content: (
          <ThirdBentoAnimation
            data={[20, 30, 25, 45, 40, 55, 75]}
            toolTipValues={[
              1234, 1678, 2101, 2534, 2967, 3400, 3833, 4266, 4700, 5133,
            ]}
          />
        ),
        title: 'Intelligent Data Analysis',
        description:
          "Transform raw data into actionable insights in seconds. Make better decisions with Atlas's real-time, adaptive intelligence.",
      },
      {
        id: 4,
        content: <FourthBentoAnimation once={false} />,
        title: 'Complete Customization',
        description:
          'Tailor Atlas to your specific needs. As an open source solution, you have full control over its capabilities, integrations, and implementation.',
      },
    ],
  },
  benefits: [
    {
      id: 1,
      text: "Automate everyday tasks with Atlas's powerful AI capabilities.",
      image: '/Device-6.png',
    },
    {
      id: 2,
      text: 'Increase productivity with autonomous task completion.',
      image: '/Device-7.png',
    },
    {
      id: 3,
      text: 'Improve focus on high-value work as Atlas handles the routine.',
      image: '/Device-8.png',
    },
    {
      id: 4,
      text: 'Access cutting-edge AI as an open source, transparent solution.',
      image: '/Device-1.png',
    },
  ],
  growthSection: {
    title: 'Open Source & Secure',
    description:
      'Where advanced security meets complete transparency—designed to protect your data while providing full access to the code.',
    items: [
      {
        id: 1,
        content: (
          <div
            className="relative flex size-full items-center justify-center overflow-hidden transition-all duration-300 hover:[mask-image:none] hover:[webkit-mask-image:none]"
            style={{
              WebkitMaskImage: `url("data:image/svg+xml,%3Csvg width='265' height='268' viewBox='0 0 265 268' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fillRule='evenodd' clipRule='evenodd' d='M121.384 4.5393C124.406 1.99342 128.319 0.585938 132.374 0.585938C136.429 0.585938 140.342 1.99342 143.365 4.5393C173.074 29.6304 210.174 45.6338 249.754 50.4314C253.64 50.9018 257.221 52.6601 259.855 55.3912C262.489 58.1223 264.005 61.6477 264.13 65.3354C265.616 106.338 254.748 146.9 232.782 182.329C210.816 217.759 178.649 246.61 140.002 265.547C137.645 266.701 135.028 267.301 132.371 267.298C129.715 267.294 127.1 266.686 124.747 265.526C86.0991 246.59 53.9325 217.739 31.9665 182.309C10.0005 146.879 -0.867679 106.317 0.618784 65.3147C0.748654 61.6306 2.26627 58.1102 4.9001 55.3833C7.53394 52.6565 11.1121 50.9012 14.9945 50.4314C54.572 45.6396 91.6716 29.6435 121.384 4.56V4.5393Z' fill='black'/%3E%3C/svg%3E")`,
              maskImage: `url("data:image/svg+xml,%3Csvg width='265' height='268' viewBox='0 0 265 268' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fillRule='evenodd' clipRule='evenodd' d='M121.384 4.5393C124.406 1.99342 128.319 0.585938 132.374 0.585938C136.429 0.585938 140.342 1.99342 143.365 4.5393C173.074 29.6304 210.174 45.6338 249.754 50.4314C253.64 50.9018 257.221 52.6601 259.855 55.3912C262.489 58.1223 264.005 61.6477 264.13 65.3354C265.616 106.338 254.748 146.9 232.782 182.329C210.816 217.759 178.649 246.61 140.002 265.547C137.645 266.701 135.028 267.301 132.371 267.298C129.715 267.294 127.1 266.686 124.747 265.526C86.0991 246.59 53.9325 217.739 31.9665 182.309C10.0005 146.879 -0.867679 106.317 0.618784 65.3147C0.748654 61.6306 2.26627 58.1102 4.9001 55.3833C7.53394 52.6565 11.1121 50.9012 14.9945 50.4314C54.572 45.6396 91.6716 29.6435 121.384 4.56V4.5393Z' fill='black'/%3E%3C/svg%3E")`,
              WebkitMaskSize: 'contain',
              maskSize: 'contain',
              WebkitMaskRepeat: 'no-repeat',
              maskPosition: 'center',
            }}
          >
            <div className="absolute top-[55%] md:top-[58%] left-[55%] md:left-[57%] -translate-x-1/2 -translate-y-1/2  size-full z-10">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="227"
                height="244"
                viewBox="0 0 227 244"
                fill="none"
                className="size-[90%] md:size-[85%] object-contain fill-background"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M104.06 3.61671C106.656 1.28763 110.017 0 113.5 0C116.983 0 120.344 1.28763 122.94 3.61671C148.459 26.5711 180.325 41.2118 214.322 45.6008C217.66 46.0312 220.736 47.6398 222.999 50.1383C225.262 52.6369 226.563 55.862 226.67 59.2357C227.947 96.7468 218.612 133.854 199.744 166.267C180.877 198.68 153.248 225.074 120.052 242.398C118.028 243.454 115.779 244.003 113.498 244C111.216 243.997 108.969 243.441 106.948 242.379C73.7524 225.055 46.1231 198.661 27.2556 166.248C8.38807 133.835 -0.947042 96.7279 0.329744 59.2168C0.441295 55.8464 1.74484 52.6258 4.00715 50.1311C6.26946 47.6365 9.34293 46.0306 12.6777 45.6008C46.6725 41.2171 78.5389 26.5832 104.06 3.63565V3.61671Z"
                />
              </svg>
            </div>
            <div className="absolute top-[58%] md:top-[60%] left-1/2 -translate-x-1/2 -translate-y-1/2  size-full z-20">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="245"
                height="282"
                viewBox="0 0 245 282"
                className="size-full object-contain fill-accent"
              >
                <g filter="url(#filter0_dddd_2_33)">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M113.664 7.33065C116.025 5.21236 119.082 4.04126 122.25 4.04126C125.418 4.04126 128.475 5.21236 130.836 7.33065C154.045 28.2076 183.028 41.5233 213.948 45.5151C216.984 45.9065 219.781 47.3695 221.839 49.6419C223.897 51.9144 225.081 54.8476 225.178 57.916C226.339 92.0322 217.849 125.781 200.689 155.261C183.529 184.74 158.4 208.746 128.209 224.501C126.368 225.462 124.323 225.962 122.248 225.959C120.173 225.956 118.13 225.45 116.291 224.484C86.0997 208.728 60.971 184.723 43.811 155.244C26.6511 125.764 18.1608 92.015 19.322 57.8988C19.4235 54.8334 20.6091 51.9043 22.6666 49.6354C24.7242 47.3665 27.5195 45.906 30.5524 45.5151C61.4706 41.5281 90.4531 28.2186 113.664 7.34787V7.33065Z"
                  />
                </g>
                <defs>
                  <filter
                    id="filter0_dddd_2_33"
                    x="0.217041"
                    y="0.0412598"
                    width="244.066"
                    height="292.917"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="3" />
                    <feGaussianBlur stdDeviation="3.5" />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="BackgroundImageFix"
                      result="effect1_dropShadow_2_33"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="12" />
                    <feGaussianBlur stdDeviation="6" />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="effect1_dropShadow_2_33"
                      result="effect2_dropShadow_2_33"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="27" />
                    <feGaussianBlur stdDeviation="8" />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="effect2_dropShadow_2_33"
                      result="effect3_dropShadow_2_33"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="48" />
                    <feGaussianBlur stdDeviation="9.5" />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="effect3_dropShadow_2_33"
                      result="effect4_dropShadow_2_33"
                    />
                    <feBlend
                      mode="normal"
                      in="SourceGraphic"
                      in2="effect4_dropShadow_2_33"
                      result="shape"
                    />
                  </filter>
                </defs>
              </svg>
            </div>
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-30">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="81"
                height="80"
                viewBox="0 0 81 80"
                className="fill-background"
              >
                <g filter="url(#filter0_iiii_2_34)">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M20.5 36V28C20.5 22.6957 22.6071 17.6086 26.3579 13.8579C30.1086 10.1071 35.1957 8 40.5 8C45.8043 8 50.8914 10.1071 54.6421 13.8579C58.3929 17.6086 60.5 22.6957 60.5 28V36C62.6217 36 64.6566 36.8429 66.1569 38.3431C67.6571 39.8434 68.5 41.8783 68.5 44V64C68.5 66.1217 67.6571 68.1566 66.1569 69.6569C64.6566 71.1571 62.6217 72 60.5 72H20.5C18.3783 72 16.3434 71.1571 14.8431 69.6569C13.3429 68.1566 12.5 66.1217 12.5 64V44C12.5 41.8783 13.3429 39.8434 14.8431 38.3431C16.3434 36.8429 18.3783 36 20.5 36ZM52.5 28V36H28.5V28C28.5 24.8174 29.7643 21.7652 32.0147 19.5147C34.2652 17.2643 37.3174 16 40.5 16C43.6826 16 46.7348 17.2643 48.9853 19.5147C51.2357 21.7652 52.5 24.8174 52.5 28Z"
                  />
                </g>
                <defs>
                  <filter
                    id="filter0_iiii_2_34"
                    x="12.5"
                    y="8"
                    width="56"
                    height="70"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                  >
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feBlend
                      mode="normal"
                      in="SourceGraphic"
                      in2="BackgroundImageFix"
                      result="shape"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="1" />
                    <feGaussianBlur stdDeviation="1" />
                    <feComposite
                      in2="hardAlpha"
                      operator="arithmetic"
                      k2="-1"
                      k3="1"
                    />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="shape"
                      result="effect1_innerShadow_2_34"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="3" />
                    <feGaussianBlur stdDeviation="1.5" />
                    <feComposite
                      in2="hardAlpha"
                      operator="arithmetic"
                      k2="-1"
                      k3="1"
                    />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="effect1_innerShadow_2_34"
                      result="effect2_innerShadow_2_34"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="8" />
                    <feGaussianBlur stdDeviation="2.5" />
                    <feComposite
                      in2="hardAlpha"
                      operator="arithmetic"
                      k2="-1"
                      k3="1"
                    />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="effect2_innerShadow_2_34"
                      result="effect3_innerShadow_2_34"
                    />
                    <feColorMatrix
                      in="SourceAlpha"
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                      result="hardAlpha"
                    />
                    <feOffset dy="14" />
                    <feGaussianBlur stdDeviation="3" />
                    <feComposite
                      in2="hardAlpha"
                      operator="arithmetic"
                      k2="-1"
                      k3="1"
                    />
                    <feColorMatrix
                      type="matrix"
                      values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"
                    />
                    <feBlend
                      mode="normal"
                      in2="effect3_innerShadow_2_34"
                      result="effect4_innerShadow_2_34"
                    />
                  </filter>
                </defs>
              </svg>
            </div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className="size-full"
            >
              <FlickeringGrid
                className="size-full"
                gridGap={4}
                squareSize={2}
                maxOpacity={0.5}
              />
            </motion.div>
          </div>
        ),

        title: 'Open Source Security',
        description:
          'Benefit from the security of open source code that thousands of eyes can review, audit, and improve.',
      },
      {
        id: 2,
        content: (
          <div className="relative flex size-full max-w-lg items-center justify-center overflow-hidden [mask-image:linear-gradient(to_top,transparent,black_50%)] -translate-y-20">
            <Globe className="top-28" />
          </div>
        ),

        title: 'Community Powered',
        description:
          "Join a thriving community of developers and users continuously enhancing and expanding Atlas's capabilities.",
      },
    ],
  },
  quoteSection: {
    quote:
      ' Atlas has transformed how we approach everyday tasks. The level of automation it provides, combined with its open source nature, makes it an invaluable tool for our entire organization.',
    author: {
      name: 'Alex Johnson',
      role: 'CTO, Innovatech',
      image: 'https://randomuser.me/api/portraits/men/91.jpg',
    },
  },
  pricing: {
    title: 'Open Source & Free Forever',
    description:
      ' Atlas is 100% open source and free to use. No hidden fees, no premium features locked behind paywalls.',
    pricingItems: [
      {
        name: 'Community',
        href: '#',
        price: 'Free',
        period: 'forever',
        yearlyPrice: 'Free',
        features: [
          'Full agent capabilities',
          'Unlimited usage',
          'Full source code access',
          'Community support',
        ],
        description: 'Perfect for individual users and developers',
        buttonText: 'Hire Atlas',
        buttonColor: 'bg-accent text-primary',
        isPopular: false,
      },
      {
        name: 'Self-Hosted',
        href: '#',
        price: 'Free',
        period: 'forever',
        yearlyPrice: 'Free',
        features: [
          'Full agent capabilities',
          'Unlimited usage',
          'Full source code access',
          'Custom deployment',
          'Local data storage',
          'Integration with your tools',
          'Full customization',
          'Community support',
        ],
        description: 'Ideal for organizations with specific requirements',
        buttonText: 'View Docs',
        buttonColor: 'bg-secondary text-white',
        isPopular: true,
      },
      {
        name: 'Enterprise',
        href: '#',
        price: 'Custom',
        period: '',
        yearlyPrice: 'Custom',
        features: [
          'Everything in Self-Hosted',
          'Priority support',
          'Custom development',
          'Dedicated hosting',
          'SLA guarantees',
        ],
        description: 'For large teams needing custom implementations',
        buttonText: 'Contact Us',
        buttonColor: 'bg-primary text-primary-foreground',
        isPopular: false,
      },
    ],
  },
  testimonials: [
    {
      id: '1',
      name: 'Alex Rivera',
      role: 'CTO at InnovateTech',
      img: 'https://randomuser.me/api/portraits/men/91.jpg',
      description: (
        <p>
          The AI-driven analytics from #QuantumInsights have revolutionized our
          product development cycle.
          <Highlight>
            Insights are now more accurate and faster than ever.
          </Highlight>{' '}
          A game-changer for tech companies.
        </p>
      ),
    },
    {
      id: '2',
      name: 'Samantha Lee',
      role: 'Marketing Director at NextGen Solutions',
      img: 'https://randomuser.me/api/portraits/women/12.jpg',
      description: (
        <p>
          Implementing #AIStream&apos;s customer prediction model has
          drastically improved our targeting strategy.
          <Highlight>Seeing a 50% increase in conversion rates!</Highlight>{' '}
          Highly recommend their solutions.
        </p>
      ),
    },
    {
      id: '3',
      name: 'Raj Patel',
      role: 'Founder & CEO at StartUp Grid',
      img: 'https://randomuser.me/api/portraits/men/45.jpg',
      description: (
        <p>
          As a startup, we need to move fast and stay ahead. #CodeAI&apos;s
          automated coding assistant helps us do just that.
          <Highlight>Our development speed has doubled.</Highlight> Essential
          tool for any startup.
        </p>
      ),
    },
    {
      id: '4',
      name: 'Emily Chen',
      role: 'Product Manager at Digital Wave',
      img: 'https://randomuser.me/api/portraits/women/83.jpg',
      description: (
        <p>
          #VoiceGen&apos;s AI-driven voice synthesis has made creating global
          products a breeze.
          <Highlight>Localization is now seamless and efficient.</Highlight> A
          must-have for global product teams.
        </p>
      ),
    },
    {
      id: '5',
      name: 'Michael Brown',
      role: 'Data Scientist at FinTech Innovations',
      img: 'https://randomuser.me/api/portraits/men/1.jpg',
      description: (
        <p>
          Leveraging #DataCrunch&apos;s AI for our financial models has given us
          an edge in predictive accuracy.
          <Highlight>
            Our investment strategies are now powered by real-time data
            analytics.
          </Highlight>{' '}
          Transformative for the finance industry.
        </p>
      ),
    },
    {
      id: '6',
      name: 'Linda Wu',
      role: 'VP of Operations at LogiChain Solutions',
      img: 'https://randomuser.me/api/portraits/women/5.jpg',
      description: (
        <p>
          #LogiTech&apos;s supply chain optimization tools have drastically
          reduced our operational costs.
          <Highlight>
            Efficiency and accuracy in logistics have never been better.
          </Highlight>{' '}
        </p>
      ),
    },
    {
      id: '7',
      name: 'Carlos Gomez',
      role: 'Head of R&D at EcoInnovate',
      img: 'https://randomuser.me/api/portraits/men/14.jpg',
      description: (
        <p>
          By integrating #GreenTech&apos;s sustainable energy solutions,
          we&apos;ve seen a significant reduction in carbon footprint.
          <Highlight>
            Leading the way in eco-friendly business practices.
          </Highlight>{' '}
          Pioneering change in the industry.
        </p>
      ),
    },
    {
      id: '8',
      name: 'Aisha Khan',
      role: 'Chief Marketing Officer at Fashion Forward',
      img: 'https://randomuser.me/api/portraits/women/56.jpg',
      description: (
        <p>
          #TrendSetter&apos;s market analysis AI has transformed how we approach
          fashion trends.
          <Highlight>
            Our campaigns are now data-driven with higher customer engagement.
          </Highlight>{' '}
          Revolutionizing fashion marketing.
        </p>
      ),
    },
    {
      id: '9',
      name: 'Tom Chen',
      role: 'Director of IT at HealthTech Solutions',
      img: 'https://randomuser.me/api/portraits/men/18.jpg',
      description: (
        <p>
          Implementing #MediCareAI in our patient care systems has improved
          patient outcomes significantly.
          <Highlight>
            Technology and healthcare working hand in hand for better health.
          </Highlight>{' '}
          A milestone in medical technology.
        </p>
      ),
    },
    {
      id: '10',
      name: 'Sofia Patel',
      role: 'CEO at EduTech Innovations',
      img: 'https://randomuser.me/api/portraits/women/73.jpg',
      description: (
        <p>
          #LearnSmart&apos;s AI-driven personalized learning plans have doubled
          student performance metrics.
          <Highlight>
            Education tailored to every learner&apos;s needs.
          </Highlight>{' '}
          Transforming the educational landscape.
        </p>
      ),
    },
    {
      id: '11',
      name: 'Jake Morrison',
      role: 'CTO at SecureNet Tech',
      img: 'https://randomuser.me/api/portraits/men/25.jpg',
      description: (
        <p>
          With #CyberShield&apos;s AI-powered security systems, our data
          protection levels are unmatched.
          <Highlight>
            Ensuring safety and trust in digital spaces.
          </Highlight>{' '}
          Redefining cybersecurity standards.
        </p>
      ),
    },
    {
      id: '12',
      name: 'Nadia Ali',
      role: 'Product Manager at Creative Solutions',
      img: 'https://randomuser.me/api/portraits/women/78.jpg',
      description: (
        <p>
          #DesignPro&apos;s AI has streamlined our creative process, enhancing
          productivity and innovation.
          <Highlight>Bringing creativity and technology together.</Highlight> A
          game-changer for creative industries.
        </p>
      ),
    },
    {
      id: '13',
      name: 'Omar Farooq',
      role: 'Founder at Startup Hub',
      img: 'https://randomuser.me/api/portraits/men/54.jpg',
      description: (
        <p>
          #VentureAI&apos;s insights into startup ecosystems have been
          invaluable for our growth and funding strategies.
          <Highlight>
            Empowering startups with data-driven decisions.
          </Highlight>{' '}
          A catalyst for startup success.
        </p>
      ),
    },
  ],
  faqSection: {
    title: 'Frequently Asked Questions',
    description:
      "Answers to common questions about  Atlas and its capabilities. If you have any other questions, please don't hesitate to contact us.",
    faQitems: [
      {
        id: 1,
        question: 'What is an AI Agent?',
        answer:
          'An AI Agent is an intelligent software program that can perform tasks autonomously, learn from interactions, and make decisions to help achieve specific goals. It combines artificial intelligence and machine learning to provide personalized assistance and automation.',
      },
      {
        id: 2,
        question: 'How does  Atlas work?',
        answer:
          ' Atlas works by analyzing your requirements, leveraging advanced AI algorithms to understand context, and executing tasks based on your instructions. It can integrate with your workflow, learn from feedback, and continuously improve its performance.',
      },
      {
        id: 3,
        question: 'Is  Atlas really free?',
        answer:
          'Yes,  Atlas is completely free and open source. We believe in democratizing AI technology and making it accessible to everyone. You can use it, modify it, and contribute to its development without any cost.',
      },
      {
        id: 4,
        question: 'Can I integrate Atlas with my existing tools?',
        answer:
          'Yes,  Atlas is designed to be highly compatible with popular tools and platforms. We offer APIs and pre-built integrations for seamless connection with your existing workflow tools and systems.',
      },
      {
        id: 5,
        question: 'How can I contribute to  Atlas?',
        answer:
          'You can contribute to  Atlas by submitting pull requests on GitHub, reporting bugs, suggesting new features, or helping with documentation. Join our Discord community to connect with other contributors and Hire Atlas.',
      },
      {
        id: 6,
        question: 'How does  Atlas save me time?',
        answer:
          ' Atlas automates repetitive tasks, streamlines workflows, and provides quick solutions to common challenges. This automation and efficiency can save hours of manual work, allowing you to focus on more strategic activities.',
      },
    ],
  },
  ctaSection: {
    id: 'cta',
    title: 'Start Using  Atlas Today',
    backgroundImage: '/holo.png',
    button: {
      text: 'Get Started for free',
      href: '/auth',
    },
    subtext: 'The generalist AI Agent that acts on your behalf',
  },
  footerLinks: [
    {
      title: '',
      links: [
        { id: 1, title: 'About', url: 'https://atlasagents.ai' },
        { id: 3, title: 'Contact', url: 'mailto:<EMAIL>' },
        { id: 4, title: 'Careers', url: 'https://atlasagents.ai/careers' },
      ],
    },
    {
      title: 'Resources',
      links: [
        {
          id: 5,
          title: 'Documentation',
          url: 'https://github.com/-ai/Atlas',
        },
        { id: 7, title: 'Discord', url: 'https://discord.gg/Py6pCBUUPw' },
        { id: 8, title: 'GitHub', url: 'https://github.com/-ai/Atlas' },
      ],
    },
    {
      title: 'Legal',
      links: [
        {
          id: 9,
          title: 'Privacy Policy',
          url: 'https://atlasagents.ai/legal?tab=privacy',
        },
        {
          id: 10,
          title: 'Terms of Service',
          url: 'https://atlasagents.ai/legal?tab=terms',
        },
        {
          id: 11,
          title: 'License Apache 2.0',
          url: 'https://github.com/-ai/Atlas/blob/main/LICENSE',
        },
      ],
    },
  ],
  useCases: [
    {
      id: 'competitor-analysis',
      title: 'Competitor Analysis',
      description:
        'Analyze the market for my next company in the healthcare industry, located in the UK. Give me the major players, their market size, strengths, and weaknesses, and add their website URLs. Once done, generate a PDF report.',
      category: 'research',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75L13.75 4.25H7.75C6.64543 4.25 5.75 5.14543 5.75 6.25V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M18 9L14 9C13.4477 9 13 8.55228 13 8L13 4"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9.5 14.5L11 13L12.5 14.5L14.5 12.5"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/2fbf0552-87d6-4d12-be25-d54f435bc493',
    },
    {
      id: 'vc-list',
      title: 'VC List',
      description:
        'Give me the list of the most important VC Funds in the United States based on Assets Under Management. Give me website URLs, and if possible an email to reach them out.',
      category: 'finance',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 4.75L19.25 9L12 13.25L4.75 9L12 4.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9.25 11.5L4.75 14L12 18.25L19.25 14L14.6722 11.5"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1444653614773-995cb1ef9efa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/a172382b-aa77-42a2-a3e1-46f32a0f9c37',
    },
    {
      id: 'candidate-search',
      title: 'Looking for Candidates',
      description:
        "Go on LinkedIn, and find 10 profiles available - they are not working right now - for a junior software engineer position, who are located in Munich, Germany. They should have at least one bachelor's degree in Computer Science or anything related to it, and 1-year of experience in any field/role.",
      category: 'recruitment',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M17.25 10C17.25 12.8995 14.8995 15.25 12 15.25C9.10051 15.25 6.75 12.8995 6.75 10C6.75 7.10051 9.10051 4.75 12 4.75C14.8995 4.75 17.25 7.10051 17.25 10Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M8.25 14.75L5.25 19.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M15.75 14.75L18.75 19.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/d9e39c94-4f6f-4b5a-b1a0-b681bfe0dee8',
    },
    {
      id: 'company-trip',
      title: 'Planning Company Trip',
      description:
        "Generate a route plan for my company. We should go to California. We'll be 8 people. Compose the trip from the departure (Paris, France) to the activities we can do considering that the trip will be 7 days long - departure on the 21st of Jun 2025.",
      category: 'travel',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.75 8.75C4.75 7.64543 5.64543 6.75 6.75 6.75H17.25C18.3546 6.75 19.25 7.64543 19.25 8.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V8.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M8 4.75V8.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M16 4.75V8.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M7.75 10.75H16.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/23f7d904-eb66-4a9c-9247-b9704ddfd233',
    },
    {
      id: 'excel-spreadsheet',
      title: 'Working on Excel',
      description:
        'My company asked to set up an Excel spreadsheet with all the information about Italian lottery games (Lotto, 10eLotto, and Million Day). Based on that, generate and send me a spreadsheet with all the basic information (public ones).',
      category: 'data',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.75 6.75C4.75 5.64543 5.64543 4.75 6.75 4.75H17.25C18.3546 4.75 19.25 5.64543 19.25 6.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V6.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9.75 8.75V19"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M5 8.25H19"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/2a147a3a-3778-4624-8285-42474c8c1c9c',
    },
    {
      id: 'speaker-prospecting',
      title: 'Automate Event Speaker Prospecting',
      description:
        "Find 20 AI ethics speakers from Europe who've spoken at conferences in the past year. Scrapes conference sites, cross-references LinkedIn and YouTube, and outputs contact info + talk summaries.",
      category: 'research',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.75 19.2502H18.25C18.8023 19.2502 19.25 18.8025 19.25 18.2502V5.75C19.25 5.19772 18.8023 4.75 18.25 4.75H5.75C5.19772 4.75 4.75 5.19772 4.75 5.75V18.2502C4.75 18.8025 5.19772 19.2502 5.75 19.2502Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9.75 8.75C9.75 9.44036 9.19036 10 8.5 10C7.80964 10 7.25 9.44036 7.25 8.75C7.25 8.05964 7.80964 7.5 8.5 7.5C9.19036 7.5 9.75 8.05964 9.75 8.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M19.25 13.75L14.75 9.25L7.25 16.75"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/6830cc6d-3fbd-492a-93f8-510a5f48ce50',
    },
    {
      id: 'scientific-papers',
      title: 'Summarize and Cross-Reference Scientific Papers',
      description:
        'Research and compare scientific papers talking about Alcohol effects on our bodies during the last 5 years. Generate a report about the most important scientific papers talking about the topic I wrote before.',
      category: 'research',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.75 6.75C4.75 5.64543 5.64543 4.75 6.75 4.75H17.25C18.3546 4.75 19.25 5.64543 19.25 6.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V6.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9.75 8.75V19"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M5 8.25H19"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/a106ef9f-ed97-46ee-8e51-7bfaf2ac3c29',
    },
    {
      id: 'lead-generation',
      title: 'Research + First Contact Draft',
      description:
        'Research my potential customers (B2B) on LinkedIn. They should be in the clean tech industry. Find their websites and their email addresses. After that, based on the company profile, generate a personalized first contact email.',
      category: 'sales',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.75 11.75L10.25 6.25L14.75 10.75L19.25 6.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M5.75 19.25H18.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M12 11.25V19.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1552581234-26160f608093?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/c3472df7-adc1-4d5f-9927-4f8f513ec2fe',
    },
    {
      id: 'seo-analysis',
      title: 'SEO Analysis',
      description:
        "Based on my website atlasagents.ai, generate an SEO report analysis, find top-ranking pages by keyword clusters, and identify topics I'm missing.",
      category: 'marketing',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.75 11.75L10.25 6.25L14.75 10.75L19.25 6.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M19.25 6.25V19.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4.75 6.25V19.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4.75 19.25H19.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/cf756e02-fee9-4281-a0e4-76ac850f1ac9',
    },
    {
      id: 'personal-trip',
      title: 'Generate a Personal Trip',
      description:
        'Generate a personal trip to London, with departure from Bangkok on the 1st of May. The trip will last 10 days. Find an accommodation in the center of London, with a rating on Google reviews of at least 4.5.',
      category: 'travel',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.75 8.75C4.75 7.64543 5.64543 6.75 6.75 6.75H17.25C18.3546 6.75 19.25 7.64543 19.25 8.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V8.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M8 4.75V8.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M16 4.75V8.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M7.75 10.75H16.25"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/8442cc76-ac8b-438c-b539-4b93909a2218',
    },
    {
      id: 'funded-startups',
      title: 'Recently Funded Startups',
      description:
        'Go on Crunchbase, Dealroom, and TechCrunch, filter by Series A funding rounds in the SaaS Finance Space, and build a report with company data, founders, and contact info for outbound sales.',
      category: 'finance',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 4.75L19.25 9L12 13.25L4.75 9L12 4.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9.25 11.5L4.75 14L12 18.25L19.25 14L14.6722 11.5"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1444653614773-995cb1ef9efa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/f04c871c-6bf5-4464-8e9c-5351c9cf5a60',
    },
    {
      id: 'scrape-forums',
      title: 'Scrape Forum Discussions',
      description:
        'I need to find the best beauty centers in Rome, but I want to find them by using open forums that speak about this topic. Go on Google, and scrape the forums by looking for beauty center discussions located in Rome.',
      category: 'research',
      featured: true,
      icon: (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M5.75 19.2502H18.25C18.8023 19.2502 19.25 18.8025 19.25 18.2502V5.75C19.25 5.19772 18.8023 4.75 18.25 4.75H5.75C5.19772 4.75 4.75 5.19772 4.75 5.75V18.2502C4.75 18.8025 5.19772 19.2502 5.75 19.2502Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M9.75 8.75C9.75 9.44036 9.19036 10 8.5 10C7.80964 10 7.25 9.44036 7.25 8.75C7.25 8.05964 7.80964 7.5 8.5 7.5C9.19036 7.5 9.75 8.05964 9.75 8.75Z"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M19.25 13.75L14.75 9.25L7.25 16.75"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      image:
        'https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',
      url: 'https://atlasagents.ai/share/53bcd4c7-40d6-4293-9f69-e2638ddcfad8',
    },
  ],
};

export type SiteConfig = typeof siteConfig;
