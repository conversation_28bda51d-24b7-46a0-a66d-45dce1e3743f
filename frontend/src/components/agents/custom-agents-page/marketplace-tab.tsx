'use client';

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { SearchBar } from './search-bar';
import { MarketplaceSectionHeader } from './marketplace-section-header';
import { AgentCard } from './agent-card';
import type { MarketplaceTemplate } from '@/components/agents/installation/types';

interface MarketplaceTabProps {
  marketplaceSearchQuery: string;
  setMarketplaceSearchQuery: (value: string) => void;
  marketplaceFilter: 'all' | 'Atlas' | 'community';
  setMarketplaceFilter: (value: 'all' | 'Atlas' | 'community') => void;
  marketplaceLoading: boolean;
  allMarketplaceItems: MarketplaceTemplate[];
  AtlasTeamItems: MarketplaceTemplate[];
  communityItems: MarketplaceTemplate[];
  installingItemId: string | null;
  onInstallClick: (item: MarketplaceTemplate, e?: React.MouseEvent) => void;
  getItemStyling: (item: MarketplaceTemplate) => { avatar: string; color: string };
}

export const MarketplaceTab = ({
  marketplaceSearchQuery,
  setMarketplaceSearchQuery,
  marketplaceFilter,
  setMarketplaceFilter,
  marketplaceLoading,
  allMarketplaceItems,
  AtlasTeamItems,
  communityItems,
  installingItemId,
  onInstallClick,
  getItemStyling
}: MarketplaceTabProps) => {
  return (
    <div className="space-y-6 mt-8">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <SearchBar
          placeholder="Search agents..."
          value={marketplaceSearchQuery}
          onChange={setMarketplaceSearchQuery}
        />
        <Select value={marketplaceFilter} onValueChange={(value: 'all' | 'Atlas' | 'community') => setMarketplaceFilter(value)}>
          <SelectTrigger className="w-[180px] h-12 rounded-xl">
            <SelectValue placeholder="Filter agents" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Agents</SelectItem>
            <SelectItem value="Atlas"> Verified</SelectItem>
            <SelectItem value="community">Community</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {marketplaceLoading ? (
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="bg-card rounded-2xl overflow-hidden shadow-sm">
              <Skeleton className="h-48" />
              <div className="p-6 space-y-3">
                <Skeleton className="h-5 rounded" />
                <div className="space-y-2">
                  <Skeleton className="h-4 rounded" />
                  <Skeleton className="h-4 rounded w-3/4" />
                </div>
                <Skeleton className="h-10 rounded-full" />
              </div>
            </div>
          ))}
        </div>
      ) : allMarketplaceItems.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {marketplaceSearchQuery
              ? "No templates found matching your criteria. Try adjusting your search or filters."
              : "No agent templates are currently available in the marketplace."}
          </p>
        </div>
      ) : (
        <div className="space-y-12">
          {marketplaceFilter === 'all' ? (
            <>
              {AtlasTeamItems.length > 0 && (
                <div className="space-y-6">
                  <MarketplaceSectionHeader
                    title="Verified by "
                    subtitle="Official agents, maintained and supported"
                  />
                  <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {AtlasTeamItems.map((item) => (
                      <AgentCard
                        key={item.id}
                        mode="marketplace"
                        data={item}
                        styling={getItemStyling(item)}
                        isActioning={installingItemId === item.id}
                        onPrimaryAction={onInstallClick}
                        onClick={() => onInstallClick(item)}
                      />
                    ))}
                  </div>
                </div>
              )}
              {communityItems.length > 0 && (
                <div className="space-y-6">
                  <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {communityItems.map((item) => (
                      <AgentCard
                        key={item.id}
                        mode="marketplace"
                        data={item}
                        styling={getItemStyling(item)}
                        isActioning={installingItemId === item.id}
                        onPrimaryAction={onInstallClick}
                        onClick={() => onInstallClick(item)}
                      />
                    ))}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {allMarketplaceItems.map((item) => (
                <AgentCard
                  key={item.id}
                  mode="marketplace"
                  data={item}
                  styling={getItemStyling(item)}
                  isActioning={installingItemId === item.id}
                  onPrimaryAction={onInstallClick}
                  onClick={() => onInstallClick(item)}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
