'use client';
import Link from 'next/link';
import GoogleSignIn from '@/components/GoogleSignIn';
import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { signIn, signUp } from './actions';
import { SubmitButton } from '@/components/ui/submit-button';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  MailCheck,
  Loader2,
  ChevronDown,
} from 'lucide-react';
import { useAuth } from '@/components/AuthProvider';
import Image from 'next/image';

function LoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading } = useAuth();

  const mode = searchParams.get('mode');
  const returnUrl = searchParams.get('returnUrl');
  const message = searchParams.get('message');
  const isSignUp = mode === 'signup';

  // Email dropdown state
  const [showEmailSignIn, setShowEmailSignIn] = useState(false);

  // Redirect if user is already logged in, checking isLoading state
  useEffect(() => {
    if (!isLoading && user) {
      router.push(returnUrl || '/dashboard');
    }
  }, [user, isLoading, router, returnUrl]);

  // Determine if message is a success message
  const isSuccessMessage =
    message &&
    (message.includes('Check your email') ||
      message.includes('Account created') ||
      message.includes('success'));

  // Registration success state
  const [registrationSuccess, setRegistrationSuccess] =
    useState(!!isSuccessMessage);
  const [registrationEmail, setRegistrationEmail] = useState('');

  // Set registration success state from URL params
  useEffect(() => {
    if (isSuccessMessage) {
      setRegistrationSuccess(true);
    }
  }, [isSuccessMessage]);

  // Form handlers
  const handleSignIn = async (prevState: any, formData: FormData) => {
    if (returnUrl) {
      formData.append('returnUrl', returnUrl);
    } else {
      formData.append('returnUrl', '/dashboard');
    }
    const result = await signIn(prevState, formData);
    if (
      result &&
      typeof result === 'object' &&
      'success' in result &&
      result.success &&
      'redirectTo' in result
    ) {
      window.location.href = result.redirectTo as string;
      return null;
    }
    return result;
  };

  const handleSignUp = async (prevState: any, formData: FormData) => {
    const email = formData.get('email') as string;
    setRegistrationEmail(email);
    if (returnUrl) {
      formData.append('returnUrl', returnUrl);
    }
    formData.append('origin', window.location.origin);
    const result = await signUp(prevState, formData);
    if (
      result &&
      typeof result === 'object' &&
      'success' in result &&
      result.success &&
      'redirectTo' in result
    ) {
      window.location.href = result.redirectTo as string;
      return null;
    }
    if (result && typeof result === 'object' && 'message' in result) {
      const resultMessage = result.message as string;
      if (resultMessage.includes('Check your email')) {
        setRegistrationSuccess(true);
        const params = new URLSearchParams(window.location.search);
        params.set('message', resultMessage);
        const newUrl =
          window.location.pathname +
          (params.toString() ? '?' + params.toString() : '');
        window.history.pushState({ path: newUrl }, '', newUrl);
        return result;
      }
    }
    return result;
  };

  // Show loading spinner while checking auth state
  if (isLoading) {
    return (
      <main className="flex flex-col items-center justify-center min-h-screen w-full">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </main>
    );
  }

  // Registration success view
  if (registrationSuccess) {
    return (
      <main className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="w-full divide-y divide-border">
          <section className="w-full relative overflow-hidden">
            <div className="relative flex flex-col items-center w-full px-6">
              {/* Background elements from the original view */}
              <div className="absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10" />
                <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
                <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />
              </div>
              <div className="absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10" />
                <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10" />
                <div className="absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10" />
              </div>
              <div className="absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"></div>
              {/* Success content */}
              <div className="relative z-10 pt-24 pb-8 max-w-xl mx-auto h-full w-full flex flex-col gap-2 items-center justify-center">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-green-50 dark:bg-green-950/20 rounded-full p-4 mb-6">
                    <MailCheck className="h-12 w-12 text-green-500 dark:text-green-400" />
                  </div>
                  <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary mb-4">
                    Check your email
                  </h1>
                  <p className="text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight max-w-md mb-2">
                    We've sent a confirmation link to:
                  </p>
                  <p className="text-lg font-medium mb-6">
                    {registrationEmail || 'your email address'}
                  </p>
                  <div className="bg-green-50 dark:bg-green-950/20 border border-green-100 dark:border-green-900/50 rounded-lg p-6 mb-8 max-w-md w-full">
                    <p className="text-sm text-green-800 dark:text-green-400 leading-relaxed">
                      Click the link in the email to activate your account. If
                      you don't see the email, check your spam folder.
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4 w-full max-w-sm">
                    <Link
                      href="/"
                      className="flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all"
                    >
                      Return to home
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>
    );
  }

  return (
    <main className="flex flex-col items-center justify-center min-h-screen w-full relative overflow-hidden">
      {/* Blurred Dashboard Background */}
      <div className="absolute inset-0 w-full h-full">
        {/* Mobile Background */}
        <div className="block md:hidden">
          <Image
            src="/mobile-dashboard.png"
            alt="Dashboard Preview"
            fill
            className="object-cover"
            style={{ filter: 'blur(20px)' }}
            priority
          />
        </div>
        {/* Desktop Background */}
        <div className="hidden md:block">
          <Image
            src="/dekstop-dashboard.png"
            alt="Dashboard Preview"
            fill
            className="object-cover"
            style={{ filter: 'blur(20px)' }}
            priority
          />
        </div>
        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black/50" />
      </div>

      {/* Auth Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6">
        <div className="w-full max-w-md">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <Image
                src="/Thumbnails.png"
                alt="Atlas"
                width={200}
                height={80}
                className="object-contain"
                priority
              />
            </div>
          </div>

          {/* Error Messages */}
          {message && !isSuccessMessage && (
            <div className="mb-6 p-4 rounded-lg flex items-center gap-3 bg-red-500/20 border border-red-500/30 text-red-200 backdrop-blur-sm">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <span className="text-sm font-medium">{message}</span>
            </div>
          )}

          {/* Custom Google Sign In Button with Dropdown */}
          <div className="w-full relative">
            {/* Main Button Container - Single seamless button */}
            <div className="relative w-full">
              {/* Google Sign In Button (full width) */}
              <GoogleSignIn returnUrl={returnUrl || undefined} />
              {/* Dropdown Arrow Overlay - positioned over the right side */}
              <button
                onClick={() => setShowEmailSignIn(!showEmailSignIn)}
                className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center rounded-full hover:bg-background/20 dark:hover:bg-white/10 transition-all z-20"
                type="button"
              >
                <ChevronDown
                  className={`h-4 w-4 text-foreground transition-transform ${showEmailSignIn ? 'rotate-180' : ''}`}
                />
              </button>
            </div>

            {/* Email Sign In Dropdown */}
            {showEmailSignIn && (
              <div className="mt-4 p-6 bg-background/60 dark:bg-[#F9FAFB]/[0.02] backdrop-blur-sm rounded-xl border-0">
                <h3 className="text-foreground text-lg font-medium mb-4 text-center">
                  {isSignUp ? 'Create Account with Email' : 'Sign in with Email'}
                </h3>
                <form className="space-y-4">
                  <div>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Email address"
                      className="h-12 rounded-full bg-background/80 dark:bg-[#F9FAFB]/[0.05] border-0 text-foreground placeholder:text-muted-foreground"
                      required
                    />
                  </div>
                  <div>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      placeholder="Password"
                      className="h-12 rounded-full bg-background/80 dark:bg-[#F9FAFB]/[0.05] border-0 text-foreground placeholder:text-muted-foreground"
                      required
                    />
                  </div>
                  {isSignUp && (
                    <div>
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        placeholder="Confirm password"
                        className="h-12 rounded-full bg-background/80 dark:bg-[#F9FAFB]/[0.05] border-0 text-foreground placeholder:text-muted-foreground"
                        required
                      />
                    </div>
                  )}
                  <div className="space-y-3 pt-2">
                    {!isSignUp ? (
                      <>
                        <SubmitButton
                          formAction={handleSignIn}
                          className="w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md font-medium"
                          pendingText="Signing in..."
                        >
                          Sign in
                        </SubmitButton>
                        <Link
                          href={`/auth?mode=signup${returnUrl ? `&returnUrl=${returnUrl}` : ''}`}
                          className="flex h-12 items-center justify-center w-full text-center rounded-full bg-background/40 dark:bg-[#F9FAFB]/[0.02] text-foreground hover:bg-background/60 dark:hover:bg-[#F9FAFB]/[0.05] transition-all"
                        >
                          Create new account
                        </Link>
                      </>
                    ) : (
                      <>
                        <SubmitButton
                          formAction={handleSignUp}
                          className="w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md font-medium"
                          pendingText="Creating account..."
                        >
                          Sign up
                        </SubmitButton>
                        <Link
                          href={`/auth${returnUrl ? `?returnUrl=${returnUrl}` : ''}`}
                          className="flex h-12 items-center justify-center w-full text-center rounded-full bg-background/40 dark:bg-[#F9FAFB]/[0.02] text-foreground hover:bg-background/60 dark:hover:bg-[#F9FAFB]/[0.05] transition-all"
                        >
                          Back to sign in
                        </Link>
                      </>
                    )}
                  </div>
                </form>
              </div>
            )}
          </div>

          {/* Landing Page Link */}
          <div className="mt-8 text-center">
            <Link href="/landing" className="text-white/60 hover:text-white transition-colors text-sm">
              atlasagents/landing
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}

export default function Login() {
  return (
    <Suspense
      fallback={
        <main className="flex flex-col items-center justify-center min-h-screen w-full">
          <div className="w-12 h-12 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
        </main>
      }
    >
      <LoginContent />
    </Suspense>
  );
}