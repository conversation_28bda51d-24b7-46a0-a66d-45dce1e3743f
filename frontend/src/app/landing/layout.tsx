import React from "react";

import { LargeNameFooter } from "@/components/landing/large-name-footer";
import { <PERSON>Header } from "@/components/landing/hero-header";

// This layout applies only to the routes within the (mainPages) group
export default function MainPagesLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Environment variable check can remain if needed for other parts of the layout
  const hasEnvVars = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Removed the header constant and its JSX definition

  // Define footer content with LargeNameFooter
  const footer = <LargeNameFooter />;

  return (
    <div className="flex flex-col min-h-screen">
      <HeroHeader />
      {/* Main content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      {footer && (
        <footer>
          <div className="w-full max-w-screen py-6">
            {footer}
          </div>
        </footer>
      )}
    </div>
  );
}
