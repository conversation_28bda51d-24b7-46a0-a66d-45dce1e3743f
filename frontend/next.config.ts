import type { NextConfig } from 'next';

const nextConfig = (): NextConfig => ({
  output: (process.env.NEXT_OUTPUT as 'standalone') || undefined,
  images: {
    domains: [
      'framerusercontent.com',
      'upload.wikimedia.org',
      'cdn.prod.website-files.com',
      'onethingdigital.com',
      'oxymor-ns.tailus.io',
      'cdn.jsdelivr.net',
      'randomuser.me',
      'images.unsplash.com',
      'images.squarespace-cdn.com',
      'media.licdn.com',
    ],
  },
});

export default nextConfig;
